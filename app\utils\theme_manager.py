import json
from app.utils.settings_manager import get_setting, set_setting

THEME_SYSTEM = "System"
THEME_QDARKSTYLE = "QDarkStyle"
THEME_QTMATERIAL = "qt-material"

def get_current_theme():
    return get_setting("theme", THEME_SYSTEM)

def set_current_theme(theme_name):
    set_setting("theme", theme_name)

def apply_theme(app):
    theme = get_current_theme()
    if theme == THEME_QDARKSTYLE:
        import qdarkstyle
        app.setStyleSheet(qdarkstyle.load_stylesheet_pyqt5())
    elif theme == THEME_QTMATERIAL:
        from qt_material import apply_stylesheet
        apply_stylesheet(app, theme='dark_teal.xml')
    else:
        app.setStyleSheet("")
