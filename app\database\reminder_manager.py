import logging
import sqlite3
from .db import get_connection

def get_reminder_settings():
    """データベースからグローバルリマインダー設定を取得する"""
    conn = get_connection()
    cursor = conn.cursor()
    try:
        # Check for schema mismatch by trying to select the expected column.
        # If it fails, the schema is wrong, so we drop and recreate the table.
        cursor.execute("SELECT key FROM reminder_settings LIMIT 1")
    except sqlite3.OperationalError:
        logging.warning("Schema mismatch in 'reminder_settings' table. Recreating it.")
        cursor.execute("DROP TABLE IF EXISTS reminder_settings")
        conn.commit()

    try:
        # Create the table with the correct schema if it doesn't exist
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS reminder_settings (
                key TEXT PRIMARY KEY,
                value TEXT
            )
        """)
        # Insert default values, ignoring if they already exist
        cursor.execute("INSERT OR IGNORE INTO reminder_settings (key, value) VALUES ('enabled', 'true')")
        cursor.execute("INSERT OR IGNORE INTO reminder_settings (key, value) VALUES ('default_timing', '1')")
        conn.commit()

        # Fetch the settings
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute("SELECT key, value FROM reminder_settings")
        rows = cursor.fetchall()

        settings = {'enabled': True, 'default_timing': 1} # Defaults
        for row in rows:
            if row['key'] == 'enabled':
                settings['enabled'] = row['value'].lower() == 'true'
            elif row['key'] == 'default_timing':
                settings['default_timing'] = int(row['value'])
        return settings
    except Exception as e:
        logging.error(f"リマインダー設定の取得に失敗しました: {e}")
        return {'enabled': True, 'default_timing': 1} # Return defaults on error
    finally:
        if conn:
            conn.close()

def update_reminder_settings(enabled, default_timing):
    """データベースのグローバルリマインダー設定を更新する"""
    conn = get_connection()
    cursor = conn.cursor()
    try:
        cursor.execute("UPDATE reminder_settings SET value = ? WHERE key = 'enabled'", (str(enabled).lower(),))
        cursor.execute("UPDATE reminder_settings SET value = ? WHERE key = 'default_timing'", (str(default_timing),))
        conn.commit()
        return True
    except Exception as e:
        logging.error(f"リマインダー設定の保存に失敗しました: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def get_task_reminder_setting(task_id):
    """タスク固有のリマインダー設定を取得する。見つからない場合はグローバル設定を返す。"""
    conn = get_connection()
    try:
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute("SELECT reminder_enabled, reminder_days FROM tasks WHERE id = ?", (task_id,))
        task_row = cursor.fetchone()

        if task_row:
            enabled = bool(task_row['reminder_enabled']) if task_row['reminder_enabled'] is not None else True
            days = task_row['reminder_days']
            return {'enabled': enabled, 'days': days}
        else:
            global_settings = get_reminder_settings()
            return {'enabled': global_settings.get('enabled', True), 'days': None}
    except Exception as e:
        logging.error(f"タスク(ID:{task_id})のリマインダー設定取得に失敗: {e}")
        global_settings = get_reminder_settings()
        return {'enabled': global_settings.get('enabled', True), 'days': None}
    finally:
        conn.close()
