from PyQt5.QtCore import QObject, pyqtSignal, QDate
from PyQt5.QtWidgets import QInputDialog, QMessageBox, QDialog
from app.services.task_service import TaskService
from app.models.task import Task as TaskModel
from app.views.dialogs.task_dialogs import AddTaskDialog, EditTaskDialog
from datetime import datetime, date
from app.database.reminder_manager import get_reminder_settings, get_task_reminder_setting


class TaskController(QObject):
    task_list_updated = pyqtSignal(list)
    project_list_updated = pyqtSignal(list)
    task_detail_updated = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)

    def __init__(self, main_controller=None):
        super().__init__()
        self.task_service = TaskService()
        self.main_controller = main_controller
        self.view = main_controller.view if main_controller else None

    def add_task_dialog(self):
        """Show add task dialog and create new task"""
        print("TaskController: add_task_dialog called")
        projects = self.task_service.get_all_project_names()
        reminder_settings = get_reminder_settings()
        dialog = AddTaskDialog(projects=projects, reminder_settings=reminder_settings, parent=self.view)

        if dialog.exec_() == QDialog.Accepted:
            task_data = dialog.get_task_data()
            try:
                # Parse dates and times from dialog data
                due_date_obj = self.main_controller._parse_date(task_data.get('due_date'))
                start_date_obj = self.main_controller._parse_date(task_data.get('start_date'))
                
                new_task = self.task_service.create_task(
                    title=task_data['title'],
                    description=task_data.get('description'),
                    due_date=due_date_obj,
                    status=task_data.get('status', '未着手'),
                    project=task_data.get('project') if task_data.get('project') and task_data.get('project') != 'なし' else None,
                    completed=task_data.get('completed', False),
                    repeat=task_data.get('repeat'),
                    start_date=start_date_obj,
                    actual_start_date = start_date_obj,  # Set actual_start_date to start_date if provided
                    estimated_time=float(task_data.get('estimated_time')) if task_data.get('estimated_time') else None,
                    reminder_enabled=task_data.get('reminder_enabled', False),
                    reminder_days=int(task_data.get('reminder_days')) if task_data.get('reminder_days') else None
                )
                if new_task:
                    print(f"Task created: {new_task.id} - {new_task.title}")
                    if self.main_controller:
                        self.main_controller.load_initial_data()
                    return new_task
                else:
                    self.error_occurred.emit("タスクの作成に失敗しました。")
            except ValueError as ve:
                self.error_occurred.emit(f"タスク作成エラー: 数値入力が無効です - {ve}")
            except Exception as e:
                self.error_occurred.emit(f"タスク作成エラー: {e}")
                print(f"Error creating task: {e}")

    def edit_task_dialog(self, task_id: int):
        """Show edit task dialog and update task"""
        print(f"TaskController: edit_task_dialog for task_id {task_id}")
        try:
            task_to_edit = self.task_service.get_task_by_id(task_id)
            if not task_to_edit:
                self.error_occurred.emit(f"編集するタスク ID {task_id} が見つかりません。")
                return

            projects = self.task_service.get_all_project_names()
            global_reminder_settings = get_reminder_settings()
            task_reminder_settings = get_task_reminder_setting(task_id)
            
            dialog = EditTaskDialog(task=task_to_edit, projects=projects,
                                    reminder_settings=global_reminder_settings,
                                    task_reminder_settings=task_reminder_settings,
                                    parent=self.view)
            if dialog.exec_() == QDialog.Accepted:
                updated_data = dialog.get_task_data()
                
                # Prepare data for service update_task method
                params_to_update = {
                    'title': updated_data['title'],
                    'description': updated_data.get('description'),
                    'due_date': self.main_controller._parse_date(updated_data.get('due_date')),
                    'status': updated_data.get('status'),
                    'project': updated_data.get('project') if updated_data.get('project') and updated_data.get('project') != 'なし' else None,
                    'completed': updated_data.get('completed', task_to_edit.completed),
                    'repeat': updated_data.get('repeat'),
                    'start_date': self.main_controller._parse_date(updated_data.get('start_date')),
                    'estimated_time': float(updated_data.get('estimated_time')) if updated_data.get('estimated_time') else None,
                    'actual_time': float(updated_data.get('actual_time')) if updated_data.get('actual_time') is not None else task_to_edit.actual_time,
                    'actual_start_date': self.main_controller._parse_date(updated_data.get('actual_start_date')),
                    'actual_end_date': self.main_controller._parse_date(updated_data.get('actual_end_date')),
                    'reminder_enabled': updated_data.get('reminder_enabled', task_to_edit.reminder_enabled),
                    'reminder_days': int(updated_data.get('reminder_days')) if updated_data.get('reminder_days') else None,
                }
                
                updated_task = self.task_service.update_task(task_id, **params_to_update)
                if updated_task:
                    print(f"Task updated: {updated_task.id}")
                    if self.main_controller:
                        self.main_controller._apply_filters_and_update_view()
                        self.main_controller.select_task(task_id)
                        projects = self.task_service.get_all_project_names()
                        self.main_controller.project_list_updated.emit(projects)
                    return updated_task
                else:
                    self.error_occurred.emit("タスクの更新に失敗しました。")
            
        except ValueError as ve:
            self.error_occurred.emit(f"タスク編集エラー: 数値入力が無効です - {ve}")
        except Exception as e:
            self.error_occurred.emit(f"タスク編集エラー: {e}")
            print(f"Error editing task {task_id}: {e}")

    def change_task_status_dialog(self, task_id: int):
        """Show dialog to change task status"""
        print(f"TaskController: change_task_status_dialog for task_id {task_id}")
        task = self.task_service.get_task_by_id(task_id)
        if not task:
            self.error_occurred.emit(f"タスク ID {task_id} が見つかりません。")
            return

        # Status options consistent with original task_manager.py
        statuses = ['未着手', '進行中', '完了', '保留', '遅延']
        current_status_index = statuses.index(task.status) if task.status in statuses else 0
        
        new_status, ok = QInputDialog.getItem(self.view, "タスク状態の変更", "新しい状態を選択:", statuses, current_status_index, False)
        
        if ok and new_status and new_status != task.status:
            try:
                params_to_update = {'status': new_status}
                if new_status == "完了":
                    params_to_update['completed'] = True
                    if not task.actual_end_date:
                         params_to_update['actual_end_date'] = datetime.now()
                    if not task.actual_start_date:
                         params_to_update['actual_start_date'] = task.start_date or datetime.now()
                elif task.completed and new_status != "完了":  # If un-completing
                    params_to_update['completed'] = False
                    params_to_update['actual_end_date'] = None

                updated = self.task_service.update_task(task_id, **params_to_update)
                if updated:
                    if self.main_controller:
                        self.main_controller._apply_filters_and_update_view()
                        self.main_controller.select_task(task_id)
                    return updated
                else:
                    self.error_occurred.emit("タスク状態の更新に失敗しました。")
            except Exception as e:
                self.error_occurred.emit(f"タスク状態の更新エラー: {e}")
                print(f"Error changing task status: {e}")

    def handle_task_completion_change(self, task_id: int, is_completed: bool):
        """Handle task completion status change with repeat task logic"""
        print(f"TaskController: handle_task_completion_change for {task_id}, completed: {is_completed}")
        try:
            task = self.task_service.get_task_by_id(task_id)
            if not task:
                self.error_occurred.emit("タスクが見つかりません。")
                return

            update_params = {'completed': is_completed}
            if is_completed:
                update_params['status'] = "完了"
                if not task.actual_end_date:  # Set actual_end_date if not already set
                    update_params['actual_end_date'] = datetime.now()
                if not task.actual_start_date:  # Set actual_start_date if not already set and completing
                    update_params['actual_start_date'] = task.start_date or datetime.now()
            else:  # Task marked as not completed
                # Revert status from '完了' if it was '完了'
                if task.status == "完了":
                    update_params['status'] = "未着手"  # Or a previous status if tracked
                # actual_end_date should be cleared if task is no longer complete
                update_params['actual_end_date'] = None

            updated_task = self.task_service.update_task(task_id, **update_params)

            if updated_task:
                # Handle repeating tasks logic
                if is_completed and updated_task.repeat:
                    next_due_date = updated_task.get_next_repeat_date()
                    if next_due_date:
                        # Create a new task for the next occurrence
                        self.task_service.create_task(
                            title=updated_task.title,
                            description=updated_task.description,
                            due_date=next_due_date,
                            status='未着手',  # New repeat is not started
                            project=updated_task.project,
                            completed=False,
                            repeat=updated_task.repeat,  # Keep repeat settings
                            start_date=updated_task.start_date,  # Or adjust based on next_due_date
                            estimated_time=updated_task.estimated_time,
                            source=updated_task.source,
                            issue_key=updated_task.issue_key,  # This might need careful handling for cloned tasks
                            backlog_project_id=updated_task.backlog_project_id,
                            source_url=updated_task.source_url,
                            reminder_enabled=updated_task.reminder_enabled,
                            reminder_days=updated_task.reminder_days
                        )
                        # Add a comment to the original completed task
                        self.task_service.add_comment_to_task(
                            task_id,
                            f"次回の繰り返しタスクを作成: {next_due_date.strftime('%Y/%m/%d')}"
                        )
                
                if self.main_controller:
                    self.main_controller._apply_filters_and_update_view()  # Refresh list based on current filter
                    self.main_controller.select_task(task_id)  # Refresh details of the (now completed/uncompleted) task
                return updated_task
            else:
                self.error_occurred.emit("タスク完了状態の更新に失敗しました。")
        except Exception as e:
            self.error_occurred.emit(f"タスク完了状態の更新エラー: {e}")
            print(f"Error updating task completion: {e}")

    def delete_task_confirmation(self, task_id: int, task_title: str = None):
        """Show confirmation dialog and delete task"""
        print(f"TaskController: delete_task_confirmation for task_id {task_id}")
        if not task_title:  # Fetch if not provided (e.g. from context menu)
            task = self.task_service.get_task_by_id(task_id)
            if task:
                task_title = task.title
            else:
                self.error_occurred.emit(f"削除するタスク ID {task_id} が見つかりません。")
                return

        reply = QMessageBox.question(self.view, 'タスク削除の確認',
                                     f"タスク '{task_title}' (ID: {task_id}) を本当に削除しますか？",
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            try:
                # Check if this project needs to be removed from project list
                task_to_delete = self.task_service.get_task_by_id(task_id)
                project_of_deleted_task = task_to_delete.project if task_to_delete else None

                success = self.task_service.delete_task(task_id)
                if success:
                    print(f"Task {task_id} deleted.")
                    if self.main_controller:
                        self.main_controller._apply_filters_and_update_view()
                        self.main_controller.select_task(None)
                        
                        # Refresh project list if the deleted task was the last one for its project
                        if project_of_deleted_task:
                            remaining_tasks_in_project = self.task_service.get_tasks_by_filter(
                                current_filter="プロジェクト",
                                project_name_filter=project_of_deleted_task
                            )
                            if not remaining_tasks_in_project:
                                projects = self.task_service.get_all_project_names()
                                self.main_controller.project_list_updated.emit(projects)
                    return True
                else:
                    self.error_occurred.emit(f"タスク ID {task_id} の削除に失敗しました。")
            except Exception as e:
                self.error_occurred.emit(f"タスク削除エラー: {e}")
                print(f"Error deleting task {task_id}: {e}")
        return False

    def delete_multiple_tasks_confirmation(self, task_ids: list):
        """Show confirmation dialog and delete multiple tasks"""
        print(f"TaskController: delete_multiple_tasks_confirmation for {len(task_ids)} tasks")
        if not task_ids:
            return

        reply = QMessageBox.question(self.view, '複数タスク削除の確認',
                                     f"{len(task_ids)} 件のタスクを本当に削除しますか？",
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            deleted_count = 0
            errors = []
            projects_affected = set()

            for task_id_val in task_ids:
                try:
                    task_to_delete = self.task_service.get_task_by_id(task_id_val)
                    if task_to_delete and task_to_delete.project:
                        projects_affected.add(task_to_delete.project)
                    
                    if self.task_service.delete_task(task_id_val):
                        deleted_count += 1
                    else:
                        errors.append(f"ID {task_id_val} の削除に失敗")
                except Exception as e:
                    errors.append(f"ID {task_id_val} 削除中にエラー: {str(e)[:50]}")
            
            print(f"Deleted {deleted_count} tasks. Errors: {len(errors)}")
            if errors:
                self.error_occurred.emit(f"{len(errors)}件のタスク削除でエラー発生。")
            
            if self.main_controller:
                self.main_controller._apply_filters_and_update_view()
                self.main_controller.select_task(None)

                # Refresh project list if any affected project now has no tasks
                refreshed_projects = False
                if projects_affected:  # Only check if there were projects to check
                    for proj_name in projects_affected:
                        if proj_name:  # Ensure project name is not None
                            remaining = self.task_service.get_tasks_by_filter("プロジェクト", proj_name)
                            if not remaining:
                                refreshed_projects = True
                                break
                if refreshed_projects:
                     all_projs = self.task_service.get_all_project_names()
                     self.main_controller.project_list_updated.emit(all_projs)
            
            return deleted_count

    def add_comment_and_save_actuals(self, task_id: int, comment_text: str,
                                     actual_time: float, 
                                     actual_start_qdate: QDate, 
                                     actual_end_qdate: QDate):
        """Add comment and save actual time/dates"""
        print(f"TaskController: add_comment_and_save_actuals for task_id {task_id}")
        if not task_id:
            self.error_occurred.emit("コメントを追加するタスクが選択されていません。")
            return

        task_to_update = self.task_service.get_task_by_id(task_id)
        if not task_to_update:
            self.error_occurred.emit("タスクが見つかりません。")
            return

        try:
            params_to_update = {}
            if actual_time is not None and actual_time >= 0:  # Allow 0
                params_to_update['actual_time'] = actual_time
            
            actual_start_dt = self.main_controller._format_qdate_to_datetime(actual_start_qdate)
            if actual_start_dt:
                params_to_update['actual_start_date'] = actual_start_dt

            actual_end_dt = self.main_controller._format_qdate_to_datetime(actual_end_qdate)
            if actual_end_dt and task_to_update.completed:  # Only save actual_end_date if task is completed
                 params_to_update['actual_end_date'] = actual_end_dt
            elif not task_to_update.completed:  # Clear actual_end_date if task is not completed
                 params_to_update['actual_end_date'] = None

            if params_to_update:
                updated_task = self.task_service.update_task(task_id, **params_to_update)
                if not updated_task:
                    self.error_occurred.emit("実績の保存に失敗しました。")
                    # Even if actuals saving failed, proceed to add comment if text is present
            
            comment_added_successfully = False
            if comment_text and comment_text.strip():
                new_comment = self.task_service.add_comment_to_task(task_id, comment_text.strip())
                if new_comment:
                    print(f"Comment added to task {task_id}: {new_comment.id}")
                    comment_added_successfully = True
                else:
                    self.error_occurred.emit("コメントの追加に失敗しました。タスクが存在しないか、他のエラーが発生しました。")
            
            if self.main_controller:
                self.main_controller._apply_filters_and_update_view() 
                self.main_controller.select_task(task_id)

            return comment_added_successfully

        except ValueError as ve:
            self.error_occurred.emit(f"実績保存エラー: 数値入力が無効です - {ve}")
            print(f"Error in add_comment_and_save_actuals (ValueError): {ve}")
        except Exception as e:
            self.error_occurred.emit(f"コメントと実績の保存中に予期せぬエラーが発生しました: {e}")
            print(f"Error in add_comment_and_save_actuals (Exception): {e}")

    def edit_comment_dialog(self, task_id: int, comment_id: int):
        """Show dialog to edit comment"""
        print(f"TaskController: edit_comment_dialog for task {task_id}, comment {comment_id}")
        
        try:
            comment_obj = self.task_service.get_comment_by_id(comment_id)
            
            if not comment_obj or comment_obj.task_id != task_id:
                self.error_occurred.emit(f"編集するコメント ID {comment_id} が見つかりません。")
                return

            new_text, ok = QInputDialog.getMultiLineText(self.view, "コメント編集", "コメント内容を編集:", comment_obj.text)
            
            if ok and new_text.strip() and new_text.strip() != comment_obj.text:  # Check if text actually changed
                updated_comment = self.task_service.update_comment(comment_id, new_text.strip())
                if updated_comment:
                    print(f"Comment {comment_id} updated.")
                    if self.main_controller:
                        self.main_controller.select_task(task_id)  # Refresh detail panel
                    return updated_comment
                else:
                    self.error_occurred.emit("コメントの更新に失敗しました。")
            elif ok and not new_text.strip():  # User tried to save an empty comment
                self.error_occurred.emit("コメント内容は空にできません。")
            # If 'ok' is True but text is unchanged, do nothing. If 'ok' is False, dialog was cancelled.

        except Exception as e:
            self.error_occurred.emit(f"コメント編集エラー: {e}")
            print(f"Error editing comment {comment_id}: {e}")

    def delete_comment_confirmation(self, task_id: int, comment_id: int):
        """Show confirmation dialog and delete comment"""
        print(f"TaskController: delete_comment_confirmation for task {task_id}, comment {comment_id}")
        reply = QMessageBox.question(self.view, 'コメント削除の確認',
                                     f"コメント ID {comment_id} を本当に削除しますか？",
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            try:
                success = self.task_service.delete_comment(comment_id)
                if success:
                    print(f"Comment {comment_id} deleted.")
                    if self.main_controller:
                        self.main_controller.select_task(task_id)  # Refresh detail panel
                    return True
                else:
                    self.error_occurred.emit(f"コメント ID {comment_id} の削除に失敗しました。")
            except Exception as e:
                self.error_occurred.emit(f"コメント削除エラー: {e}")
                print(f"Error deleting comment {comment_id}: {e}")
        return False

    def select_task(self, task_id: int):
        print(f"Controller: select_task called for task_id {task_id}")
        if task_id is None:
            self.task_detail_updated.emit({})
            return
        try:
            task_orm_obj = self.task_service.get_task_by_id(task_id)
            if task_orm_obj:
                task_detail_dict = self.main_controller._format_task_details_for_view(task_orm_obj)
                self.task_detail_updated.emit(task_detail_dict)
            else:
                self.error_occurred.emit(f"タスク ID {task_id} が見つかりません。")
                self.task_detail_updated.emit({})
        except Exception as e:
            self.error_occurred.emit(f"タスク詳細の取得エラー: {e}")
            print(f"Error selecting task {task_id}: {e}")
            self.task_detail_updated.emit({})