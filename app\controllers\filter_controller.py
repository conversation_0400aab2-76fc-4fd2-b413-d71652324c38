from PyQt5.QtCore import QObject, pyqtSignal
from app.services.task_service import TaskService
from typing import Optional

class FilterController(QObject):
    """Controller responsible for task filtering functionality."""
    
    # Signals
    task_list_updated = pyqtSignal(list)
    filter_label_updated = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, main_controller=None):
        super().__init__()
        self.task_service = TaskService()
        self.main_controller = main_controller
        self.current_active_filter = "すべてのタスク"  
        self.current_project_filter_name = None
    
    def _format_task_for_view(self, task_orm_object):
        """Delegate to main controller for formatting tasks."""
        if self.main_controller:
            return self.main_controller._format_task_for_view(task_orm_object)
        return None
    
    def _apply_filters_and_update_view(self):
        """Applies current filters and updates the view. Filter label is set by calling methods."""
        try:            
            filter_label_text = "🗃️ すべてのタスク"  
            if self.current_active_filter == "今日のタスク":
                filter_label_text = "🗓️ 今日のタスク"
            elif self.current_active_filter == "未完了タスク":
                filter_label_text = "🏃 未完了タスク"
            elif self.current_active_filter == "完了したタスク":
                filter_label_text = "✅ 完了したタスク"
            elif self.current_active_filter == "プロジェクト" and self.current_project_filter_name:
                filter_label_text = f"📂 プロジェクト: {self.current_project_filter_name}"
            
            print(f"FilterController: Calling get_tasks_by_filter with current_filter={self.current_active_filter}, project_name_filter={self.current_project_filter_name}")
            tasks_orm = self.task_service.get_tasks_by_filter(
                current_filter=self.current_active_filter,
                project_name_filter=self.current_project_filter_name
            )
            tasks_for_view = [self._format_task_for_view(t) for t in tasks_orm]
            self.task_list_updated.emit(tasks_for_view)
            self.filter_label_updated.emit(filter_label_text)
        except Exception as e:
            import traceback
            self.error_occurred.emit(f"タスクのフィルタリング中にエラーが発生しました: {e}")
            print(f"Error applying filters: {e}")
            print(f"Error details: {traceback.format_exc()}")
    
    def filter_today_tasks(self):
        """Filter tasks due today."""
        print("FilterController: filter_today_tasks called")
        self.current_active_filter = "今日のタスク"
        self.current_project_filter_name = None
        self._apply_filters_and_update_view()
    
    def filter_all_tasks(self):
        """Filter to show all tasks."""
        print("FilterController: filter_all_tasks called")
        self.current_active_filter = "すべてのタスク"
        self.current_project_filter_name = None
        self._apply_filters_and_update_view()
    
    def filter_incomplete_tasks(self):
        """Filter to show incomplete tasks."""
        print("FilterController: filter_incomplete_tasks called")
        self.current_active_filter = "未完了タスク"
        self.current_project_filter_name = None
        self._apply_filters_and_update_view()

    def filter_completed_tasks(self):
        """Filter to show completed tasks."""
        print("FilterController: filter_completed_tasks called")
        self.current_active_filter = "完了したタスク"  
        self.current_project_filter_name = None
        self._apply_filters_and_update_view()
    
    def filter_by_project(self, project_name_from_view: Optional[str]):
        """
        Filter tasks by project name.
        
        Args:
            project_name_from_view: Project name which might have "📂 " prefix or be "🗃️ すべてのタスク"
        """
        print(f"FilterController: filter_by_project called for '{project_name_from_view}'")
        if project_name_from_view is None or project_name_from_view == "なし":
            self.current_active_filter = "プロジェクト"
            self.current_project_filter_name = "なし"
            self._apply_filters_and_update_view()
        elif project_name_from_view == "🗃️ すべてのタスク":
            self.filter_all_tasks()
        else:
            # Assuming project_name_from_view is "📂 Project Name" or just "Project Name"
            cleaned_project_name = project_name_from_view.replace('📂 ', '').strip()
            self.current_active_filter = "プロジェクト"
            self.current_project_filter_name = cleaned_project_name
            self._apply_filters_and_update_view()
    
    def get_current_filter_state(self):
        """Return current filter state for syncing with main controller."""
        return {
            "current_active_filter": self.current_active_filter,
            "current_project_filter_name": self.current_project_filter_name
        }
