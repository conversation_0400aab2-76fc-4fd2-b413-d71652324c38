from sqlalchemy.types import TypeDecorator, String
from datetime import datetime

class CustomDateTime(TypeDecorator):
    impl = String
    
    def process_bind_param(self, value, dialect):
        if value is None:
            return None
        if isinstance(value, datetime):
            return value.strftime('%Y/%m/%d')
        return value
    
    def process_result_value(self, value, dialect):
        if isinstance(value, str) and '/' in value:
            try:
                return datetime.strptime(value, '%Y/%m/%d')
            except ValueError:
                try:
                    parts = value.split('/')
                    if len(parts) == 3:
                        year, month, day = parts
                        return datetime(int(year), int(month), int(day))
                except (ValueError, IndexError):
                    pass
        return value