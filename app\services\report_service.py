import datetime
from collections import defaultdict
from config.report_templates import DEFAULT_TEMPLATE
from app.models.task import Task
from app.models.comment import Comment

class ReportService:
    """
    Service for generating reports from tasks.
    """

    def _format_date(self, date_str_or_obj):
        """Formats a date string or a date/datetime object into YYYY-MM-DD format."""
        if not date_str_or_obj:
            return ''
        try:
            if hasattr(date_str_or_obj, 'strftime'):
                # Handles datetime.date and datetime.datetime objects
                return date_str_or_obj.strftime('%Y-%m-%d')
            
            date_str = str(date_str_or_obj)
            # Standardize the format by replacing '/' with '-'
            date_str = date_str.replace('/', '-')
            
            # Handle timestamps with time component (like "2025-05-07 00:00:00")
            if ' ' in date_str:
                date_str = date_str.split(' ')[0]
            
            # Handle ISO format (like "2025-05-07T00:00:00Z")
            if 'T' in date_str:
                date_str = date_str.split('T')[0]
            
            return date_str
        except Exception:
            return str(date_str_or_obj)

    def generate_report(self, tasks: list[Task], template: dict = None) -> str:
        """
        Generates a daily report from a list of task objects.
        """
        if template is None:
            template = DEFAULT_TEMPLATE

        tasks_by_project = defaultdict(list)

        for task in tasks:
            project_name = task.project if task.project else "未分類タスク"
            
            status = task.status or ''
            actual_start = task.actual_start_date
            actual_end = task.actual_end_date

            # Estimate actual dates if not present
            if not actual_start and status in ['進行中', '完了']:
                actual_start = task.start_date or datetime.date.today()
            if not actual_end and status == '完了':
                actual_end = datetime.date.today()

            task_info = {
                'title': task.title or 'タイトルなし',
                'issue_key': '', # Local tasks don't have an issue key
                'start_date': self._format_date(task.start_date),
                'due_date': self._format_date(task.due_date),
                'actual_start_date': self._format_date(actual_start),
                'actual_end_date': self._format_date(actual_end),
                'progress': status or '順調',
                'comment': task.description or '',
                'estimated_time': float(task.estimated_time or 0.0),
                'actual_time': float(task.actual_time or 0.0),
                'source': 'local',
                'comments': task.comments or []
            }
            tasks_by_project[project_name].append(task_info)

        # Sort projects
        project_keys = sorted(tasks_by_project.keys(), key=lambda x: (x == "未分類タスク", x))

        report = ""
        for project_name in project_keys:
            try:
                report += template['project_title'].format(project_name=project_name)
            except (KeyError, TypeError):
                report += f"● {project_name}\n"

            for i, task_info in enumerate(tasks_by_project[project_name], 1):
                actual_start_str = task_info['actual_start_date']
                actual_end_str = task_info['actual_end_date']
                
                if not actual_start_str and not actual_end_str:
                    actual_period = "未開始" if task_info['start_date'] else "未設定"
                elif not actual_start_str:
                    actual_period = f"? ～ {actual_end_str}"
                elif not actual_end_str:
                    actual_period = f"{actual_start_str} ～ ?"
                else:
                    actual_period = f"{actual_start_str} ～ {actual_end_str}"

                context = {**task_info, 'index': i, 'actual_period': actual_period}
                context['comment'] = context['comment'] or '特になし'

                try:
                    report += template['task_title'].format(**context)
                    report += template['task_details'].format(**context)
                except KeyError as e:
                    print(f"Template error: {e}")
                    report += f"    {i}. {task_info['title']}\n"
                    report += f"        進捗状況：{task_info['progress']}\n"

                if task_info.get('comments'):
                    try:
                        report += template['comment_history_header']
                    except KeyError:
                        report += "        コメント履歴：\n"
                    
                    for comment in task_info['comments']:
                        timestamp_str = comment.timestamp.strftime('%Y/%m/%d %H:%M') if comment.timestamp else ''
                        try:
                            report += template['comment_entry'].format(
                                timestamp=timestamp_str,
                                text=comment.text
                            )
                        except KeyError:
                             report += f"            {timestamp_str}: {comment.text}\n"
                
                try:
                    report += template['task_separator']
                except KeyError:
                    report += "\n"
            
            try:
                report += template['project_separator']
            except KeyError:
                report += "\n"

        return report.strip()
