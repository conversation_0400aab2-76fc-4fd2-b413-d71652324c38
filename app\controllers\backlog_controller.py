from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtWidgets import QMessageBox
from app.services.backlog_service import BacklogService
from app.services.task_service import TaskService
from app.utils.settings_manager import get_setting, set_setting
from app.views.dialogs.backlog_settings_dialog import BacklogSettingsDialog
import traceback

class BacklogController(QObject):
    error_occurred = pyqtSignal(str)
    def __init__(self, parent_controller):
        super().__init__(parent_controller)
        self.parent_controller = parent_controller
        self.task_service = TaskService()

    def open_backlog_settings(self):
        """Backlog設定ダイアログを開く"""
        # 現在の設定を読み込む
        backlog_settings = get_setting('backlog_credentials', [])
        
        dialog = BacklogSettingsDialog(backlog_settings, self.parent_controller.view)
        if dialog.exec_():
            new_settings = dialog.get_settings()
            set_setting('backlog_credentials', new_settings)
            QMessageBox.information(self.parent_controller.view, "成功", "Backlog設定が保存されました。")

    def fetch_backlog_tasks(self):
        """Backlogからタスクを取得してローカルDBに同期する"""
        try:
            QMessageBox.information(self.parent_controller.view, "Backlog同期", "Backlogからタスクの取得を開始します。")
            
            credentials = get_setting('backlog_credentials', [])
            active_credentials = [cred for cred in credentials if not cred.get('disabled', False)]

            if not active_credentials:
                self.error_occurred.emit("有効なBacklogアカウントが設定されていません。設定メニューからアカウントを追加してください。")
                return

            total_added_count = 0
            total_updated_count = 0

            for cred in active_credentials:
                try:
                    service = BacklogService(project_name=cred['name'])
                    backlog_tasks = service.get_tasks() # Task-like dicts
                    
                    added_count, updated_count = self.sync_tasks_to_db(backlog_tasks, cred['name'])
                    total_added_count += added_count
                    total_updated_count += updated_count

                except Exception as e:
                    self.error_occurred.emit(f"アカウント '{cred.get('name')}' のタスク取得に失敗しました: {e}")
                    traceback.print_exc()

            # Refresh the view
            self.parent_controller.load_initial_data()

            summary_message = (
                f"Backlogタスクの同期が完了しました。\n"
                f"新規追加: {total_added_count}件\n"
                f"更新: {total_updated_count}件"
            )
            QMessageBox.information(self.parent_controller.view, "同期完了", summary_message)

        except Exception as e:
            self.error_occurred.emit(f"Backlogタスクの取得中にエラーが発生しました: {e}")
            traceback.print_exc()

    def sync_tasks_to_db(self, backlog_tasks, account_name):
        """取得したタスクをデータベースに同期する"""
        added_count = 0
        updated_count = 0
        project_name = f"Backlog: {account_name}"

        for task_data in backlog_tasks:
            # Add account name to project to make it unique
            task_data['project'] = project_name
            
            existing_task = self.task_service.find_task_by_backlog_key(task_data['issue_key'])
            
            if existing_task:
                # Update existing task
                # Don't update title and description from backlog
                task_data.pop('title', None)
                task_data.pop('description', None)
                self.task_service.update_task(existing_task.id, **task_data)
                updated_count += 1
            else:
                # Add new task
                self.task_service.add_task(**task_data)
                added_count += 1
        
        return added_count, updated_count
