# テンプレート編集機能 使用ガイド

## 概要

このガイドでは、日報生成機能のテンプレート編集機能の使用方法について説明します。
テンプレート編集機能を使用することで、日報の出力形式を自由にカスタマイズできます。

## 機能の特徴

- ✅ **MVC アーキテクチャ準拠**: プロジェクトの既存のMVC構造に従って実装
- ✅ **フォーマット保持**: 缩进やスペースなどの書式を正確に保持
- ✅ **JSON ベース編集**: 直感的なJSON形式でテンプレートを編集
- ✅ **変数挿入支援**: ワンクリックで利用可能な変数を挿入
- ✅ **リアルタイムプレビュー**: テンプレート変更時に即座にプレビューを更新

## 使用方法

### 1. 日報生成ダイアログを開く

1. メインアプリケーションでタスクを選択
2. 「日報生成」ボタンをクリック
3. 日報生成ダイアログが表示される

### 2. テンプレート編集ダイアログを開く

1. 日報生成ダイアログで「テンプレート編集」ボタンをクリック
2. テンプレート編集ダイアログが表示される

### 3. テンプレートの編集

#### 新規テンプレートの作成
1. 「新規テンプレート」ボタンをクリック
2. テンプレート名を入力
3. デフォルトテンプレートがコピーされる

#### 既存テンプレートの編集
1. 左側のリストから編集したいテンプレートを選択
2. 中央のエディタでJSONフォーマットのテンプレートを編集
3. 右側の変数ボタンをクリックして変数を挿入

#### テンプレートの保存
1. 編集完了後、「保存」ボタンをクリック
2. テンプレートがファイルに保存される

## テンプレート構造

テンプレートは以下のセクションで構成されています：

```json
{
    "project_title": "● {project_name}\n",
    "task_title": "    {index}. {issue_key} {title}\n",
    "task_details": "　　　　進捗状況：{progress}\n　　　　予定期間：{start_date} ～ {due_date}\n　　　　実績期間：{actual_period}\n　　　　実績時間：{actual_time:.1f} h\n　　　　遅れ要因：特になし\n　　　　コメント：{comment}\n",
    "comment_history_header": "　　　　進捗記録：\n",
    "comment_entry": "　　　　　　　{timestamp} {text}\n",
    "task_separator": "\n",
    "project_separator": "\n"
}
```

### セクションの説明

- **project_title**: プロジェクト名の表示形式
- **task_title**: 各タスクのタイトル行の形式
- **task_details**: タスクの詳細情報の表示形式
- **comment_history_header**: コメント履歴セクションのヘッダー
- **comment_entry**: 各コメントの表示形式
- **task_separator**: タスク間の区切り文字
- **project_separator**: プロジェクト間の区切り文字

## 利用可能な変数

### プロジェクト変数
- `{project_name}`: プロジェクト名

### タスク変数
- `{title}`: タスクのタイトル
- `{issue_key}`: Issue Key（Backlogなどの外部システム用）
- `{start_date}`: 予定開始日
- `{due_date}`: 予定終了日
- `{progress}`: 進捗状況
- `{actual_start_date}`: 実績開始日
- `{actual_end_date}`: 実績終了日
- `{actual_period}`: 実績期間（自動計算）
- `{estimated_time}`: 予定時間
- `{actual_time}`: 実績時間
- `{comment}`: コメント
- `{source}`: タスクのソース
- `{index}`: タスクの番号

### コメント変数
- `{timestamp}`: コメントのタイムスタンプ
- `{text}`: コメントのテキスト

## 書式設定のコツ

### インデントの設定
- スペースを使用してインデントを設定
- 全角スペース（　）も使用可能
- `\n` で改行を表現

### 数値フォーマット
- `{actual_time:.1f}`: 小数点以下1桁で表示
- `{estimated_time:.0f}`: 整数で表示

### 特殊文字
- 改行: `\n`
- タブ: `\t`
- ダブルクォート: `\"`

## テンプレート例

### シンプルなテンプレート
```json
{
    "project_title": "■ {project_name}\n",
    "task_title": "  【{index}】{title}\n",
    "task_details": "    ・進捗: {progress}\n    ・期間: {start_date} ～ {due_date}\n    ・実績: {actual_time:.1f}h\n",
    "comment_history_header": "    ・履歴:\n",
    "comment_entry": "      [{timestamp}] {text}\n",
    "task_separator": "\n",
    "project_separator": "\n"
}
```

### 絵文字を使用したテンプレート
```json
{
    "project_title": "🚀 プロジェクト: {project_name}\n",
    "task_title": "  📋 {index}. {title}\n",
    "task_details": "      ⏰ 期間: {start_date} → {due_date}\n      📊 進捗: {progress}\n      ⏱️  実績: {actual_time:.1f}h\n",
    "comment_history_header": "      📝 履歴:\n",
    "comment_entry": "        • {timestamp}: {text}\n",
    "task_separator": "\n",
    "project_separator": "\n" + "="*50 + "\n\n"
}
```

## トラブルシューティング

### よくある問題

1. **JSONフォーマットエラー**
   - 解決方法: JSON構文を確認し、カンマやクォートの位置を修正

2. **変数が置換されない**
   - 解決方法: 変数名のスペルを確認し、利用可能な変数リストと照合

3. **インデントが表示されない**
   - 解決方法: `\n` の後にスペースや全角スペースを追加

4. **テンプレートが保存されない**
   - 解決方法: テンプレート名が重複していないか確認

## ファイル構成

- `config/custom_templates.json`: カスタムテンプレートの保存先
- `config/report_templates.py`: デフォルトテンプレートの定義
- `app/views/dialogs/template_editor_dialog.py`: テンプレート編集ダイアログ
- `app/controllers/report_controller.py`: レポート制御ロジック

## 技術仕様

- **アーキテクチャ**: MVC パターン
- **データ形式**: JSON
- **文字エンコーディング**: UTF-8
- **GUI フレームワーク**: PyQt5

---

このガイドを参考に、プロジェクトのニーズに合わせた日報テンプレートを作成してください。
