from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, 
    QLabel, QSpinBox, QCheckBox, QPushButton, QGroupBox
)
from PyQt5.QtCore import Qt

class ReminderSettingsDialog(QDialog):
    """リマインダー設定ダイアログ"""
    
    def __init__(self, initial_settings=None, parent=None):
        super().__init__(parent)
        self.setWindowTitle('リマインダー設定')
        self.setMinimumWidth(400)
        self.initial_settings = initial_settings or {'enabled': True, 'default_timing': 1}
        self.setup_ui()
        self.load_settings()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # グローバル設定グループ
        global_group = QGroupBox("リマインダーのグローバル設定")
        global_layout = QVBoxLayout()
        
        # リマインダー有効/無効
        self.enabled_checkbox = QCheckBox("リマインダー機能を有効にする")
        global_layout.addWidget(self.enabled_checkbox)
          # デフォルトのリマインド時間
        timing_layout = QHBoxLayout()
        timing_layout.addWidget(QLabel("デフォルトのリマインド時間:"))
        self.timing_spinbox = QSpinBox()
        self.timing_spinbox.setMinimum(0)
        self.timing_spinbox.setMaximum(30)  # 最大30日前まで設定可能
        self.timing_spinbox.setSuffix(" 日前")
        self.timing_spinbox.setToolTip("タスクの期限日の何日前に通知するか（0は当日通知）")
        timing_layout.addWidget(self.timing_spinbox)
        timing_layout.addStretch()
        global_layout.addLayout(timing_layout)
        
        global_group.setLayout(global_layout)
        layout.addWidget(global_group)
        
        # 説明ラベル
        info_label = QLabel(
            "注意: タスクごとのリマインド設定は、各タスクの編集画面から行えます。\n"
            "リマインダーはWindowsのトースト通知を使用します。"
        )
        info_label.setWordWrap(True)
        info_label.setStyleSheet("color: #666;")
        layout.addWidget(info_label)
        
        # ボタンレイアウト
        button_layout = QHBoxLayout()
        cancel_button = QPushButton('キャンセル')
        cancel_button.clicked.connect(self.reject)
        save_button = QPushButton('保存')
        save_button.clicked.connect(self.accept) # Changed from self.save_settings
        save_button.setDefault(True)
        
        button_layout.addWidget(cancel_button)
        button_layout.addWidget(save_button)
        
        layout.addStretch()
        layout.addLayout(button_layout)
        
    def load_settings(self):
        """渡された初期設定を読み込む"""
        self.enabled_checkbox.setChecked(self.initial_settings.get('enabled', True))
        self.timing_spinbox.setValue(self.initial_settings.get('default_timing', 1))
        
    def get_settings(self):
        """現在のUI設定を辞書として返す"""
        return {
            'enabled': self.enabled_checkbox.isChecked(),
            'default_timing': self.timing_spinbox.value()
        }
