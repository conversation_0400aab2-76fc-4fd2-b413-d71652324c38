import sqlite3
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from config import settings

engine = create_engine(settings.DATABASE_URI)

SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine
)

def init_db():
    # Import all models to ensure they are registered
    from app.models import Task, Comment, Base
    Base.metadata.create_all(bind=engine)

def get_connection():
    """Provides a raw sqlite3 database connection."""
    db_path = settings.DATABASE_URI.replace('sqlite:///', '')
    return sqlite3.connect(db_path)