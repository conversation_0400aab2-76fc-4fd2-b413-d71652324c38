from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, func
from sqlalchemy.orm import relationship
from .base import Base

class Comment(Base):
    __tablename__ = 'comments'
    
    id = Column(Integer, primary_key=True, index=True)
    text = Column(String, nullable=False) 
    task_id = Column(Integer, ForeignKey('tasks.id'), nullable=False)
    timestamp = Column(String, nullable=False) 
    # updated_at = Column(DateTime, nullable=False, server_default=func.now(), onupdate=func.now()) # コメント編集

    task = relationship("Task", back_populates="comments")
    
    def __repr__(self):
        return f"<Comment(id={self.id}, task_id={self.task_id}')>"