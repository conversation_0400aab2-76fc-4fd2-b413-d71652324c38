from PyQt5.QtWidgets import (
    QMain<PERSON>indow, QW<PERSON>t, QVBoxLayout, QHBoxLayout,
    QTableWidget, QTextEdit, QPushButton, QLabel, QTableWidgetItem,
    QSplitter, QDialog, QLineEdit, QDateEdit, QCheckBox, QMessageBox, QInputDialog,
    QGroupBox, QScrollArea, QAbstractItemView, QHeaderView, QListWidget, QListWidgetItem,
    QDoubleSpinBox, QMenu, QAction, QComboBox
)
from PyQt5.QtCore import Qt, QDate, QDateTime # QDateTime for compatibility if needed, QDate for new date handling
from PyQt5.QtGui import QColor # For styling completed tasks

from app.controllers.main_controller import MainController # Ensure this import is active
from app.utils.theme_manager import THEME_SYSTEM, THEME_QDARKSTYLE, THEME_QTMATERIAL

class MainWindow(QMainWindow):
    def __init__(self, controller: MainController): # Type hint for controller
        super().__init__()
        self.controller = controller
        # self.current_filter = 'すべてのタスク' # Filter state managed by controller
        # self.projects = [] # Data managed by controller
        # self.tasks = [] # Data managed by controller
        self._checkbox_connections = {} # To manage checkbox signal connections
        self.current_detail_task_id = None # Store ID of the task in detail panel

        self.setup_ui()
        # connect_signals is called at the end of setup_ui

    def setup_ui(self):
        self.setWindowTitle('タスク管理ツール')
        self.setGeometry(100, 100, 1200, 700)

        # --- Menu Bar ---
        self.setup_menu_bar()

        # --- Central Widget and Main Layout ---
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        # --- Left Sidebar (Navigation) ---
        nav_widget = QWidget()
        nav_layout = QVBoxLayout() # Don't set parent widget yet

        # --- Theme Chooser ---
        theme_group = QGroupBox("テーマ選択")
        theme_layout = QHBoxLayout()
        self.theme_combo = QComboBox()
        self.theme_combo.addItems([THEME_SYSTEM, THEME_QDARKSTYLE, THEME_QTMATERIAL])
        current_theme = self.controller.get_current_theme()
        self.theme_combo.setCurrentText(current_theme)
        theme_layout.addWidget(self.theme_combo)
        theme_group.setLayout(theme_layout)
        nav_layout.addWidget(theme_group)

        nav_layout.addWidget(QLabel('📌 ナビゲーション'))
        self.nav_buttons = {}
        # Actual connections will be done in connect_signals
        nav_items = {
            '今日のタスク': "filter_today_tasks", # Store method names for controller
            'すべてのタスク': "filter_all_tasks",
            '未完了タスク': "filter_incomplete_tasks",
            '完了したタスク': "filter_completed_tasks",
        }
        for label, method_name in nav_items.items():
            button = QPushButton(label)
            # button.clicked.connect(getattr(self.controller, method_name)) # Example connection
            nav_layout.addWidget(button)
            self.nav_buttons[label] = button

        nav_layout.addWidget(QLabel('🔄 Backlog 連携'))
        self.refresh_backlog_btn = QPushButton('Backlogタスク更新')
        nav_layout.addWidget(self.refresh_backlog_btn)

        self.backlog_settings_btn = QPushButton('Backlog設定')
        nav_layout.addWidget(self.backlog_settings_btn)
        
        nav_layout.addWidget(QLabel('⏰ リマインド設定'))
        self.reminder_settings_btn = QPushButton('リマインド設定')
        nav_layout.addWidget(self.reminder_settings_btn)

        nav_layout.addWidget(QLabel('📂 プロジェクト'))
        self.project_list_widget = QListWidget() # Renamed to avoid conflict with self.projects list
        self.project_list_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        nav_layout.addWidget(self.project_list_widget)
        
        self.add_project_btn = QPushButton('➕ プロジェクト追加')
        nav_layout.addWidget(self.add_project_btn)

        self.daily_report_btn = QPushButton('📝 日報生成')
        self.daily_report_btn.setStyleSheet("background-color: #f0f0f0; font-weight: bold;")
        nav_layout.addWidget(self.daily_report_btn)

        nav_layout.addStretch()
        nav_widget.setLayout(nav_layout) # Set layout to widget here


        # --- Middle Panel (Task List) ---
        task_widget = QWidget()
        task_layout_main = QVBoxLayout() # Main layout for this panel

        # Control layout for filter label and add task button
        control_layout = QHBoxLayout()
        self.filter_label = QLabel('🗃️ すべてのタスク') # Will be updated by controller
        control_layout.addWidget(self.filter_label)
        control_layout.addStretch()
        self.add_task_btn_main = QPushButton('➕ 新しいタスク') # Renamed to avoid conflict
        control_layout.addWidget(self.add_task_btn_main)
        task_layout_main.addLayout(control_layout)

        # Task Table
        self.task_table_widget = QTableWidget()
        self.task_table_widget.setColumnCount(5) # 恢复为5列
        self.task_table_widget.setHorizontalHeaderLabels(['完了', 'タイトル', '期限日', '状態', '繰り返し']) # 添加 '繰り返し'
        self.task_table_widget.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.task_table_widget.setSelectionMode(QAbstractItemView.ExtendedSelection)
        self.task_table_widget.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.task_table_widget.verticalHeader().setVisible(False)
        self.task_table_widget.setContextMenuPolicy(Qt.CustomContextMenu)

        header = self.task_table_widget.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents) # 完了列
        header.setSectionResizeMode(1, QHeaderView.Stretch)         # タイトル列をストレッチ
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents) # 期限日列
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents) # 状態列
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents) # 繰り返し列
        task_layout_main.addWidget(self.task_table_widget)
        task_widget.setLayout(task_layout_main)


        # --- Right Sidebar (Task Details) ---
        detail_panel_widget = QWidget()
        detail_main_layout = QVBoxLayout() # Main layout for this panel
        detail_main_layout.addWidget(QLabel('📋 タスク詳細'))

        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        self.detail_scroll_widget = QWidget() # Content widget for scroll area
        self.detail_scroll_layout = QVBoxLayout(self.detail_scroll_widget) # Layout for content widget

        self.detail_title = QLabel("タイトル (未選択)")
        self.detail_title.setWordWrap(True)
        self.detail_title.setStyleSheet("font-size: 14pt; font-weight: bold;")
        self.detail_scroll_layout.addWidget(self.detail_title)

        self.detail_planned_period = QLabel("予定期間:")
        self.detail_scroll_layout.addWidget(self.detail_planned_period)

        self.detail_status = QLabel("状態:")
        self.detail_scroll_layout.addWidget(self.detail_status)
        self.detail_repeat = QLabel("繰り返し:")
        self.detail_scroll_layout.addWidget(self.detail_repeat)
        self.detail_project = QLabel("プロジェクト:")        
        self.detail_scroll_layout.addWidget(self.detail_project)

        # Time layout
        time_layout = QHBoxLayout()
        self.detail_estimated_time = QLabel("予定時間:")
        self.detail_actual_time_label = QLabel("実績時間:")
        self.detail_actual_time_spin = QDoubleSpinBox()
        self.detail_actual_time_spin.setSuffix(" h")
        self.detail_actual_time_spin.setDecimals(1)
        self.detail_actual_time_spin.setSingleStep(0.5)
        self.detail_actual_time_spin.setRange(0, 999)
        time_layout.addWidget(self.detail_estimated_time)
        time_layout.addStretch()
        time_layout.addWidget(self.detail_actual_time_label)
        time_layout.addWidget(self.detail_actual_time_spin)
        self.detail_scroll_layout.addLayout(time_layout)

        # Actual period
        actual_period_group = QGroupBox("実績期間:")
        actual_period_layout = QHBoxLayout()
        self.actual_start_date_edit = QDateEdit()
        self.actual_start_date_edit.setCalendarPopup(True)
        self.actual_start_date_edit.setDisplayFormat('yyyy/MM/dd')
        self.actual_start_date_edit.setFixedWidth(120)
        self.actual_end_date_edit = QDateEdit()
        self.actual_end_date_edit.setCalendarPopup(True)
        self.actual_end_date_edit.setDisplayFormat('yyyy/MM/dd')
        self.actual_end_date_edit.setFixedWidth(120)
        actual_period_layout.addWidget(QLabel("開始:"))
        actual_period_layout.addWidget(self.actual_start_date_edit)
        actual_period_layout.addWidget(QLabel("終了:"))
        actual_period_layout.addWidget(self.actual_end_date_edit)
        actual_period_layout.addStretch()
        actual_period_group.setLayout(actual_period_layout)
        self.detail_scroll_layout.addWidget(actual_period_group)

        # Description
        desc_group = QGroupBox("説明:")
        desc_layout = QVBoxLayout()
        self.detail_description = QLabel("説明内容")
        self.detail_description.setWordWrap(True)
        desc_layout.addWidget(self.detail_description)
        desc_group.setLayout(desc_layout)
        self.detail_scroll_layout.addWidget(desc_group)

        # Comments
        comments_group = QGroupBox("コメント・メモ (進捗記録):")
        comments_layout = QVBoxLayout()
        self.comments_list_widget = QListWidget()
        self.comments_list_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        comments_layout.addWidget(self.comments_list_widget)
        comment_input_layout = QHBoxLayout()
        self.comment_input = QLineEdit()
        self.add_comment_btn = QPushButton("コメント追加 (実績時間/期間も保存)")
        self.add_comment_btn.setObjectName("add_comment_btn")
        comment_input_layout.addWidget(self.comment_input)
        comment_input_layout.addWidget(self.add_comment_btn)
        comments_layout.addLayout(comment_input_layout)
        comments_group.setLayout(comments_layout)
        self.detail_scroll_layout.addWidget(comments_group)

        # Action Buttons
        detail_button_layout = QHBoxLayout()
        self.edit_task_btn = QPushButton("編集")
        self.status_change_btn = QPushButton("状態変更...")
        self.delete_task_btn = QPushButton("削除")
        detail_button_layout.addWidget(self.edit_task_btn)
        detail_button_layout.addWidget(self.status_change_btn)
        detail_button_layout.addWidget(self.delete_task_btn)
        self.detail_scroll_layout.addLayout(detail_button_layout)
        self.detail_scroll_layout.addStretch()

        scroll_area.setWidget(self.detail_scroll_widget)
        detail_main_layout.addWidget(scroll_area)
        detail_panel_widget.setLayout(detail_main_layout)        # --- Splitter ---
        splitter = QSplitter(Qt.Horizontal)
        splitter.addWidget(nav_widget)
        splitter.addWidget(task_widget)
        splitter.addWidget(detail_panel_widget)
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 3)
        splitter.setStretchFactor(2, 2)
        
        main_layout.addWidget(splitter)
        self._connect_controller_signals() # Connect signals from controller to view slots
        self.connect_ui_signals() # Connect UI element signals to controller slots or internal view handlers    
    def setup_menu_bar(self):
        """セットアップメニューバー"""
        menu_bar = self.menuBar()

        # ファイルメニュー
        file_menu = menu_bar.addMenu('ファイル')
        
        self.settings_action = QAction('Backlog設定', self)
        file_menu.addAction(self.settings_action)
        
        self.reminder_settings_action_menu = QAction('リマインダー設定', self) # Renamed to avoid conflict
        file_menu.addAction(self.reminder_settings_action_menu)
        
        file_menu.addSeparator()
        
        self.exit_action = QAction('終了', self)
        file_menu.addAction(self.exit_action)
        
        # デバッグメニュー
        debug_menu = menu_bar.addMenu("デバッグ")
        
        self.log_action = QAction("ログを表示", self)
        debug_menu.addAction(self.log_action)
        
        self.show_settings_action = QAction("設定を表示", self) # Renamed
        debug_menu.addAction(self.show_settings_action)
        
        self.check_reminder_action = QAction('リマインダーチェックを実行', self)
        debug_menu.addAction(self.check_reminder_action)    
    def _connect_controller_signals(self):
        """Connect signals from the controller to the view's slots."""
        self.controller.task_list_updated.connect(self.update_task_list_display)
        self.controller.project_list_updated.connect(self.update_project_list_display)
        self.controller.task_detail_updated.connect(self.display_task_detail)
        self.controller.filter_label_updated.connect(self.update_filter_label)
        self.controller.error_occurred.connect(self.show_error)

    def connect_ui_signals(self): # Renamed from connect_signals
        # Menu actions
        self.settings_action.triggered.connect(self.controller.open_backlog_settings)
        self.reminder_settings_action_menu.triggered.connect(self.controller.open_reminder_settings)
        self.exit_action.triggered.connect(self.close)
        self.log_action.triggered.connect(self.controller.show_log_dialog)
        self.show_settings_action.triggered.connect(self.controller.show_app_settings_dialog)
        self.check_reminder_action.triggered.connect(self.controller.debug_check_reminders)

        # Theme chooser
        self.theme_combo.currentTextChanged.connect(self.on_theme_changed)

        # Nav buttons
        for label, button in self.nav_buttons.items():
            if label == '今日のタスク':
                button.clicked.connect(self.controller.filter_today_tasks)
            elif label == 'すべてのタスク':
                button.clicked.connect(self.controller.filter_all_tasks)
            elif label == '未完了タスク':
                button.clicked.connect(self.controller.filter_incomplete_tasks)
            elif label == '完了したタスク':
                button.clicked.connect(self.controller.filter_completed_tasks)
        
        self.refresh_backlog_btn.clicked.connect(self.controller.fetch_backlog_tasks)
        self.backlog_settings_btn.clicked.connect(self.controller.open_backlog_settings)
        self.reminder_settings_btn.clicked.connect(self.controller.open_reminder_settings)
        
        self.project_list_widget.itemClicked.connect(self.on_project_selected)
        self.project_list_widget.customContextMenuRequested.connect(self.show_project_context_menu)
        self.add_project_btn.clicked.connect(self.controller.add_project_dialog)
        self.daily_report_btn.clicked.connect(self.controller.open_daily_report_tool)

        # Middle panel signals
        self.add_task_btn_main.clicked.connect(self.controller.add_task_dialog)
        self.task_table_widget.customContextMenuRequested.connect(self.show_task_context_menu)
        self.task_table_widget.itemSelectionChanged.connect(self.on_task_selection_changed)

        # Right panel signals
        self.comments_list_widget.customContextMenuRequested.connect(self.show_comment_context_menu)
        self.add_comment_btn.clicked.connect(self.on_add_comment_button_clicked)
        self.edit_task_btn.clicked.connect(self.on_edit_task_button_clicked)
        self.status_change_btn.clicked.connect(self.on_status_change_button_clicked)
        self.delete_task_btn.clicked.connect(self.on_delete_task_button_clicked)

    def on_theme_changed(self, theme_name):
        """Handles theme change from the combobox."""
        self.controller.change_theme(theme_name)

    def get_selected_task_ids(self) -> list[int]:
        """Returns a list of selected task IDs from the table."""
        selected_task_ids = []
        selected_row_indexes = self.task_table_widget.selectionModel().selectedRows()
        for model_index in selected_row_indexes:
            row = model_index.row()
            title_item = self.task_table_widget.item(row, 1) # Title item has UserRole with task ID
            if title_item and title_item.data(Qt.UserRole) is not None:
                selected_task_ids.append(title_item.data(Qt.UserRole))
        return selected_task_ids

    def on_task_selection_changed(self):
        """Handles selection changes in the task table."""
        print("View: on_task_selection_changed triggered.")
        selected_rows = self.task_table_widget.selectionModel().selectedRows()

        if selected_rows:
            row = selected_rows[0].row()
            task_id_item = self.task_table_widget.item(row, 1) # Title item stores ID in UserRole
            if task_id_item and task_id_item.data(Qt.UserRole) is not None:
                task_id = task_id_item.data(Qt.UserRole)
                print(f"View: Task ID {task_id} selected.")
                if self.current_detail_task_id != task_id:
                    self.controller.select_task(task_id)
            else:
                print("View: Selected item has no task ID.")
                self.controller.select_task(None)
        else:
            print("View: Selection cleared.")
            if self.current_detail_task_id is not None:
                self.controller.select_task(None)

    def show_task_context_menu(self, position):
        """タスクリスト上での右クリックメニューを表示する"""
        menu = QMenu()
        selected_row_indexes = self.task_table_widget.selectionModel().selectedRows()
        
        selected_task_ids = []
        for model_index in selected_row_indexes:
            row = model_index.row()
            title_item = self.task_table_widget.item(row, 1) # Title item has UserRole with task ID
            if title_item and title_item.data(Qt.UserRole) is not None:
                selected_task_ids.append(title_item.data(Qt.UserRole))

        if not selected_task_ids:
            return

        if len(selected_task_ids) == 1:
            task_id = selected_task_ids[0]

            edit_action = QAction("編集", self)
            edit_action.triggered.connect(lambda checked=False, t_id=task_id: self.controller.edit_task_dialog(t_id))
            menu.addAction(edit_action)

            status_action = QAction("状態変更...", self)
            status_action.triggered.connect(lambda checked=False, t_id=task_id: self.controller.change_task_status_dialog(t_id))
            menu.addAction(status_action)
            
            menu.addSeparator()

            delete_action = QAction("削除", self)
            # Controller's delete_task_confirmation expects task_id and optional title
            delete_action.triggered.connect(lambda checked=False, t_id=task_id: self.controller.delete_task_confirmation(t_id))
            menu.addAction(delete_action)
        else: # Multiple tasks selected
            delete_multiple_action = QAction(f"選択した {len(selected_task_ids)} 件のタスクを削除", self)
            delete_multiple_action.triggered.connect(lambda: self.controller.delete_multiple_tasks_confirmation(selected_task_ids))
            menu.addAction(delete_multiple_action)

        if not menu.isEmpty():
            menu.exec_(self.task_table_widget.viewport().mapToGlobal(position))

    def show_comment_context_menu(self, position):
        """コメント一覧上での右クリックメニューを表示する"""
        item = self.comments_list_widget.itemAt(position)
        
        if item and self.current_detail_task_id is not None:
            menu = QMenu()
            comment_id = item.data(Qt.UserRole) # Stored comment ID

            if comment_id is not None:
                edit_action = QAction("コメントを編集", self)
                edit_action.triggered.connect(lambda checked=False, t_id=self.current_detail_task_id, c_id=comment_id: self.controller.edit_comment_dialog(t_id, c_id))
                menu.addAction(edit_action)

                delete_action = QAction("コメントを削除", self)
                delete_action.triggered.connect(lambda checked=False, t_id=self.current_detail_task_id, c_id=comment_id: self.controller.delete_comment_confirmation(t_id, c_id))
                menu.addAction(delete_action)

            if not menu.isEmpty():
                menu.exec_(self.comments_list_widget.viewport().mapToGlobal(position))

    def on_add_comment_button_clicked(self):
        """Handles the 'Add Comment' button click."""
        if self.current_detail_task_id is not None:
            comment_text = self.comment_input.text().strip()
            actual_time = self.detail_actual_time_spin.value()
            actual_start_qdate = self.actual_start_date_edit.date()
            actual_end_qdate = self.actual_end_date_edit.date()
            
            self.controller.add_comment_and_save_actuals(
                self.current_detail_task_id,
                comment_text,
                actual_time,
                actual_start_qdate,
                actual_end_qdate
            )
            self.comment_input.clear()
        else:
            self.show_error("コメントを追加するタスクが選択されていません。")
    def on_edit_task_button_clicked(self):
        if self.current_detail_task_id is not None:
            self.controller.edit_task_dialog(self.current_detail_task_id)
        else:
            self.show_error("編集するタスクが選択されていません。")

    def on_status_change_button_clicked(self):
        if self.current_detail_task_id is not None:
            self.controller.change_task_status_dialog(self.current_detail_task_id)
        else:
            self.show_error("状態を変更するタスクが選択されていません。")

    def on_delete_task_button_clicked(self):
        if self.current_detail_task_id is not None:
            # The controller's delete_task_confirmation can fetch the title if needed,
            # or we can pass it if readily available from the detail panel's title label.
            task_title = self.detail_title.text() # Get title from detail panel
            self.controller.delete_task_confirmation(self.current_detail_task_id, task_title)
        else:
            self.show_error("削除するタスクが選択されていません。")

    def on_project_selected(self, item: QListWidgetItem):
        """Handles project selection from the list."""
        if not item: # selection cleared
            return
        project_text = item.text()
        if project_text == "🗃️ すべてのタスク":
            self.controller.filter_all_tasks()
        elif project_text == "なし":
            self.controller.filter_by_project(None) 
        elif project_text.startswith('📂 '):
            project_name = project_text.replace('📂 ', '').strip()
            self.controller.filter_by_project(project_name)
        else:
            # Handle other filter options
            self.controller.filter_by_project(project_text)

    def show_project_context_menu(self, position):
        """プロジェクトリスト上での右クリックメニューを表示する"""
        item = self.project_list_widget.itemAt(position)
        if item:
            menu = QMenu()
            project_name_with_icon = item.text()
            
            # "All Tasks" item is not editable/deletable in this context
            if project_name_with_icon == '🗃️ すべてのタスク':
                return

            project_name = project_name_with_icon.replace('📂 ', '').strip()            # These actions assume projects are managed entities.
            # If projects are just attributes of tasks, these operations are more complex.
            # Controller methods for these are placeholders for now.
            rename_action = QAction(f"プロジェクト '{project_name}' をリネーム", self)
            rename_action.triggered.connect(lambda checked=False, p_name=project_name: self.controller.rename_project(p_name))
            menu.addAction(rename_action)

            delete_action = QAction(f"プロジェクト '{project_name}' を削除", self)
            delete_action.triggered.connect(lambda checked=False, p_name=project_name: self.controller.delete_project(p_name))
            menu.addAction(delete_action)

            if not menu.isEmpty():
                 menu.exec_(self.project_list_widget.mapToGlobal(position))

    # These methods are likely superseded by controller-driven updates or dialogs.
    # Commenting them out for now.
    # def on_create_task(self):
    #     pass
    #
    # def on_task_created(self, task_data):
    #     # self.update_task_list_display() # Controller should signal this
    #     pass
    #
    # def on_comment_added(self, comment_data):
    #     # if self.current_detail_task:
    #     #     task_id_to_refresh = None
    #     #     if isinstance(self.current_detail_task, dict): task_id_to_refresh = self.current_detail_task.get('db_id')
    #     #     elif hasattr(self.current_detail_task, 'db_id'): task_id_to_refresh = self.current_detail_task.db_id
    #     #
    #     #     if task_id_to_refresh and comment_data.get('task_id') == task_id_to_refresh :
    #     #          self.controller.select_task(task_id_to_refresh) # Re-fetch and display
    #     pass
    
    def show_error(self, message):
        QMessageBox.critical(self, "エラー", message)

    def clear_detail_panel(self):
        """右サイドの詳細パネルをクリアする"""
        if not hasattr(self, 'detail_title'): # Check if UI is setup
            return

        # Reset all text fields with empty state messages
        self.detail_title.setText("タスクが選択されていません")
        self.detail_title.setStyleSheet("font-size: 14pt; font-weight: bold; color: #888888;")
        
        self.detail_planned_period.setText("予定期間: 未設定")
        self.detail_planned_period.setStyleSheet("")
        
        self.detail_status.setText("状態: 未設定")
        self.detail_status.setStyleSheet("")
        
        self.detail_repeat.setText("繰り返し: 未設定")
        self.detail_repeat.setStyleSheet("")
        
        self.detail_project.setText("プロジェクト: 未設定")
            
        self.detail_estimated_time.setText("予定時間: 未設定")
        self.detail_actual_time_spin.setValue(0.0)
        
        self.actual_start_date_edit.setDate(QDate())
        self.actual_start_date_edit.setSpecialValueText(" ")
        
        self.actual_end_date_edit.setDate(QDate())
        self.actual_end_date_edit.setSpecialValueText(" ")
        
        self.detail_description.setText("説明なし")
        self.detail_description.setTextFormat(Qt.PlainText)
        
        # Clear comments with an informative message
        self.comments_list_widget.clear()
        empty_comment_item = QListWidgetItem("タスクを選択するとコメントが表示されます")
        empty_comment_item.setForeground(QColor(120, 120, 120))
        self.comments_list_widget.addItem(empty_comment_item)
        
        self.comment_input.clear()

        # Disable all interactive elements
        self.edit_task_btn.setEnabled(False)
        self.status_change_btn.setEnabled(False)
        self.delete_task_btn.setEnabled(False)
        self.comment_input.setEnabled(False)
        self.add_comment_btn.setEnabled(False)
        self.detail_actual_time_spin.setEnabled(False)
        self.actual_start_date_edit.setEnabled(False)
        self.actual_end_date_edit.setEnabled(False)
        
        self.current_detail_task_id = None

    def update_task_list_display(self, tasks_list_of_dicts):
        """Updates the task list table in the UI. Expects a list of task dicts from controller."""
        self.task_table_widget.setUpdatesEnabled(False) # Performance improvement
        
        # Disconnect old checkbox signals before clearing rows or changing row count
        # Iterate over a copy of items if modifying the dictionary during iteration
        for task_id_key, conn in list(self._checkbox_connections.items()):
            try:
                # This assumes the checkbox itself is not stored, only its connection.
                # If the widget is destroyed when rows are cleared, this might be complex.
                # A safer way is to disconnect when the widget is about to be removed or replaced.
                # For now, we rely on clearing the dict and re-connecting.
                # If checkbox widgets are reused, explicit disconnect is crucial.
                pass # For now, just clearing the dict.
            except (TypeError, RuntimeError):
                pass # Ignore errors if already disconnected or widget gone
        self._checkbox_connections.clear()
        self.task_table_widget.setRowCount(0)


        if tasks_list_of_dicts is None:
            tasks_list_of_dicts = []

        # Filter out malformed data before setting row count
        valid_tasks_data = [td for td in tasks_list_of_dicts if td and td.get('id') is not None]
        self.task_table_widget.setRowCount(len(valid_tasks_data))

        today = QDate.currentDate()
        
        for row, task_data in enumerate(valid_tasks_data):
            task_id = task_data['id']
            
            # --- 0. 完了チェックボックス ---
            checkbox_widget_container = QWidget()
            checkbox_layout = QHBoxLayout(checkbox_widget_container)
            checkbox = QCheckBox()
            checkbox_layout.addWidget(checkbox)
            checkbox_layout.setAlignment(Qt.AlignCenter)
            checkbox_layout.setContentsMargins(0,0,0,0)
            
            is_completed = task_data.get('completed', False)
            checkbox.setChecked(is_completed)
            
            # It's crucial that task_id is unique and stable for this connection management.
            # If a checkbox for a task_id already existed and was disconnected,
            # this creates a new one.
            connection = checkbox.stateChanged.connect(
                lambda state, t_id=task_id: self.controller.handle_task_completion_change(t_id, state == Qt.Checked)
            )
            self._checkbox_connections[task_id] = connection # Store by task_id
            self.task_table_widget.setCellWidget(row, 0, checkbox_widget_container)

            # --- 1. タイトル ---
            title_str = task_data.get('title', 'N/A')
            title_item = QTableWidgetItem(title_str)
            title_item.setData(Qt.UserRole, task_id)
            
            # Add project as tooltip if available
            project = task_data.get('project')
            if project:
                title_item.setToolTip(f"プロジェクト: {project}")
            
            self.task_table_widget.setItem(row, 1, title_item)

            # --- 2. 期限日 ---
            due_date_str = task_data.get('due_date', '')
            due_date_item = QTableWidgetItem(due_date_str if due_date_str else '')
            
            # Color coding for due dates
            if due_date_str and not is_completed:
                try:
                    due_date = QDate.fromString(due_date_str, "yyyy/MM/dd")
                    days_until_due = today.daysTo(due_date)
                    
                    if days_until_due < 0:  # Overdue
                        due_date_item.setForeground(QColor(255, 0, 0))  # Red
                        due_date_item.setToolTip("期限超過")
                    elif days_until_due == 0:  # Due today
                        due_date_item.setForeground(QColor(255, 165, 0))  # Orange
                        due_date_item.setToolTip("今日が期限")
                    elif days_until_due <= 3:  # Due soon
                        due_date_item.setForeground(QColor(255, 140, 0))  # Dark Orange
                        due_date_item.setToolTip(f"あと{days_until_due}日")
                    elif days_until_due <= 7:  # Due within a week
                        due_date_item.setForeground(QColor(0, 128, 0))  # Green
                        due_date_item.setToolTip(f"あと{days_until_due}日")
                except:
                    pass  # If date parsing fails, no special coloring
                    
            self.task_table_widget.setItem(row, 2, due_date_item)

            # --- 3. 状態 ---
            status_str = task_data.get('status', 'N/A')
            status_item = QTableWidgetItem(status_str)
            
            # Color coding for status
            if status_str == '未着手':
                status_item.setForeground(QColor(128, 128, 128))  # Gray
            elif status_str == '進行中':
                status_item.setForeground(QColor(0, 0, 255))  # Blue
            elif status_str == '完了':
                status_item.setForeground(QColor(0, 128, 0))  # Green
            elif status_str == '中断':
                status_item.setForeground(QColor(255, 0, 0))  # Red
                
            self.task_table_widget.setItem(row, 3, status_item)

            # --- 4. 繰り返し ---
            repeat_str = task_data.get('repeat', 'なし')
            repeat_item = QTableWidgetItem(repeat_str)
            
            if repeat_str != 'なし':
                repeat_item.setForeground(QColor(138, 43, 226))  # BlueViolet for repeating tasks
                repeat_item.setToolTip("繰り返しタスク")
                
            self.task_table_widget.setItem(row, 4, repeat_item)
            
            # Styling based on completion
            font = title_item.font()
            font.setStrikeOut(is_completed)
            text_color = QColor(150, 150, 150) if is_completed else self.palette().color(self.foregroundRole())
          
            # Apply font to all cells in the row
            for col_idx in range(1, self.task_table_widget.columnCount()):
                item = self.task_table_widget.item(row, col_idx)
                if item:
                    item.setFont(font)
                    if is_completed:  # Only override color if completed
                        item.setForeground(text_color)
        
        # Improve table appearance
        self.task_table_widget.setAlternatingRowColors(True)
        self.task_table_widget.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                selection-background-color: #e0e0ff;
            }
            QTableWidget::item:selected {
                background-color: #e0e0ff;
            }
        """)
        
        self.task_table_widget.setUpdatesEnabled(True)

        # Restore selection
        if self.current_detail_task_id is not None:
            found_selected = False
            for r_idx in range(self.task_table_widget.rowCount()):
                id_item = self.task_table_widget.item(r_idx, 1)
                if id_item and id_item.data(Qt.UserRole) == self.current_detail_task_id:
                    # Block signals to prevent on_task_selection_changed from re-fetching
                    self.task_table_widget.blockSignals(True)
                    self.task_table_widget.selectRow(r_idx) # Select the entire row
                    self.task_table_widget.blockSignals(False)
                    found_selected = True
                    break
            if not found_selected:
                # If the selected task is no longer in the list (due to filter change), clear details
                self.clear_detail_panel()
        elif not valid_tasks_data: # If there are no tasks at all
            self.clear_detail_panel()


    def update_project_list_display(self, project_names_list):
        """プロジェクトリストUIを更新する. Expects a list of project name strings."""
        current_selection = self.project_list_widget.currentItem()
        current_project_text = current_selection.text() if current_selection else None

        self.project_list_widget.clear()
        
        # 1. Add "すべてのタスク" (All Tasks) item, as in original task_manager.py's project list
        all_tasks_item = QListWidgetItem('🗃️ すべてのタスク')
        all_tasks_item.setBackground(QColor(240, 240, 250))  # Light blue-gray background
        all_tasks_item.setForeground(QColor(0, 0, 120))  # Dark blue text
        font = all_tasks_item.font()
        font.setBold(True)
        all_tasks_item.setFont(font)
        self.project_list_widget.addItem(all_tasks_item)
        if current_project_text == '🗃️ すべてのタスク': # Restore selection if it was this item
            self.project_list_widget.setCurrentItem(all_tasks_item)

        # 2. Process "なし" (No Project) and other projects
        processed_project_names = []
        has_none_project_in_list = False # Check if "なし" is explicitly in the list from controller

        for p_name in project_names_list:
            if p_name == "なし": # Controller now sends "なし" string for None projects
                has_none_project_in_list = True
            elif p_name and p_name not in processed_project_names: # Avoid duplicates, ensure not None or empty
                 processed_project_names.append(p_name)
        
        # Add "なし" item if applicable
        if has_none_project_in_list:
            item_none = QListWidgetItem("なし") # Display "なし"
            item_none.setForeground(QColor(100, 100, 100))  # Gray text
            self.project_list_widget.addItem(item_none)
            if current_project_text == "なし":
                self.project_list_widget.setCurrentItem(item_none)
        
        # Add other sorted project items
        for name in sorted(processed_project_names):
            item = QListWidgetItem(f'📂 {name}')
            # Set alternating background colors for better readability
            if processed_project_names.index(name) % 2 == 0:
                item.setBackground(QColor(245, 245, 245))  # Very light gray
            self.project_list_widget.addItem(item)
            if current_project_text == f'📂 {name}':
                self.project_list_widget.setCurrentItem(item)
                
        # Apply a nice style to the list widget
        self.project_list_widget.setStyleSheet("""
            QListWidget {
                background-color: white;
                border: 1px solid #cccccc;
                border-radius: 4px;
            }
            QListWidget::item {
                padding: 4px;
                border-bottom: 1px solid #eeeeee;
            }
            QListWidget::item:selected {
                background-color: #e0e0ff;
                color: black;
            }
        """)

    def display_task_detail(self, task_detail_dict):
        """指定されたタスクの詳細を右サイドパネルに表示する. Expects a dict from controller."""
        if not task_detail_dict or not task_detail_dict.get('id'):
            self.clear_detail_panel()
            return

        self.current_detail_task_id = task_detail_dict.get('id')
        is_task_completed = task_detail_dict.get('completed', False)

        # Update title with completion status
        title_text = task_detail_dict.get('title', 'タイトルなし')
        if is_task_completed:
            self.detail_title.setText(f"✓ {title_text}")
            self.detail_title.setStyleSheet("font-size: 14pt; font-weight: bold; color: #757575; text-decoration: line-through;")
        else:
            self.detail_title.setText(title_text)
            self.detail_title.setStyleSheet("font-size: 14pt; font-weight: bold;")

        # Format planned period with visual cues
        planned_period_text = task_detail_dict.get('planned_period', "予定期間: 未設定")
        due_date_str = task_detail_dict.get('due_date', '')
        
        if due_date_str and not is_task_completed:
            try:
                due_date = QDate.fromString(due_date_str, "yyyy/MM/dd")
                today = QDate.currentDate()
                days_until_due = today.daysTo(due_date)
                
                if days_until_due < 0:
                    self.detail_planned_period.setStyleSheet("color: red;")
                    self.detail_planned_period.setText(f"{planned_period_text} ⚠️ 期限超過")
                elif days_until_due == 0:
                    self.detail_planned_period.setStyleSheet("color: orange;")
                    self.detail_planned_period.setText(f"{planned_period_text} ⚠️ 今日が期限")
                elif days_until_due <= 3:
                    self.detail_planned_period.setStyleSheet("color: #FF8C00;")
                    self.detail_planned_period.setText(f"{planned_period_text} ⚠️ もうすぐ期限")
                else:
                    self.detail_planned_period.setStyleSheet("")
                    self.detail_planned_period.setText(planned_period_text)
            except:
                self.detail_planned_period.setStyleSheet("")
                self.detail_planned_period.setText(planned_period_text)
        else:
            self.detail_planned_period.setStyleSheet("")
            self.detail_planned_period.setText(planned_period_text)
            
        # Status with color coding
        status_str = task_detail_dict.get('status', '未設定')
        self.detail_status.setText(f"状態: {status_str}")
        
        if status_str == '未着手':
            self.detail_status.setStyleSheet("color: #808080;")
        elif status_str == '進行中':
            self.detail_status.setStyleSheet("color: #0000FF;")
        elif status_str == '完了':
            self.detail_status.setStyleSheet("color: #008000;")
        elif status_str == '中断':
            self.detail_status.setStyleSheet("color: #FF0000;")
        else:
            self.detail_status.setStyleSheet("")
            
        # Repeat settings
        repeat_display = task_detail_dict.get('repeat_display', 'なし')
        self.detail_repeat.setText(f"繰り返し: {repeat_display}")
        if repeat_display != 'なし':
            self.detail_repeat.setStyleSheet("color: #8A2BE2;")
        else:
            self.detail_repeat.setStyleSheet("")
            
        # Project
        project_name = task_detail_dict.get('project', 'なし')
        self.detail_project.setText(f"プロジェクト: {project_name}")
              # Estimated time with better formatting
        est_time = task_detail_dict.get('estimated_time_raw')
        if est_time is not None:
            self.detail_estimated_time.setText(f"予定時間: {est_time:.1f} h")
        else:
            self.detail_estimated_time.setText("予定時間: 未設定")
        
        # Actual time value
        actual_time = task_detail_dict.get('actual_time', 0.0)
        self.detail_actual_time_spin.setValue(actual_time)
        
        # Actual start date
        actual_start_dt = task_detail_dict.get('actual_start_date_raw')
        if actual_start_dt:
            self.actual_start_date_edit.setDate(QDate(actual_start_dt.year, actual_start_dt.month, actual_start_dt.day))
        else:
            self.actual_start_date_edit.setDate(QDate())
            self.actual_start_date_edit.setSpecialValueText(" ")

        # Actual end date
        actual_end_dt = task_detail_dict.get('actual_end_date_raw')
        if actual_end_dt:
            self.actual_end_date_edit.setDate(QDate(actual_end_dt.year, actual_end_dt.month, actual_end_dt.day))
        else:
            self.actual_end_date_edit.setDate(QDate())
            self.actual_end_date_edit.setSpecialValueText(" ")

        # Description with proper formatting
        description = task_detail_dict.get('description', '説明なし')
        if description:
            # Replace newlines with HTML breaks for proper display
            description_html = description.replace('\n', '<br>')
            self.detail_description.setText(description_html)
            self.detail_description.setTextFormat(Qt.RichText)
        else:
            self.detail_description.setText('説明なし')
            self.detail_description.setTextFormat(Qt.PlainText)
        
        # Comments list with better formatting
        self.comments_list_widget.clear()
        comments = task_detail_dict.get('comments', [])
        if not comments:
             no_comment_item = QListWidgetItem("コメントはありません。")
             no_comment_item.setForeground(QColor('gray'))
             self.comments_list_widget.addItem(no_comment_item)
        else:
            for comment_data in comments:
                # Format: "{timestamp} - {text}"
                item_text = f"{comment_data.get('timestamp', '不明')} - {comment_data.get('content', 'コメントなし')}"
                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, comment_data.get('id'))
                self.comments_list_widget.addItem(item)

        # Enable/disable UI elements based on task selection and status
        is_task_selected = self.current_detail_task_id is not None
        self.edit_task_btn.setEnabled(is_task_selected)
        self.status_change_btn.setEnabled(is_task_selected)
        self.delete_task_btn.setEnabled(is_task_selected)
        self.comment_input.setEnabled(is_task_selected)
        self.add_comment_btn.setEnabled(is_task_selected)
        self.detail_actual_time_spin.setEnabled(is_task_selected)
        self.actual_start_date_edit.setEnabled(is_task_selected)
        
        # Original logic: actual_end_date_edit is enabled only if task is completed
        self.actual_end_date_edit.setEnabled(is_task_selected and is_task_completed)
        if not (is_task_selected and is_task_completed) and not actual_end_dt : # if not enabled and no date, show blank
            self.actual_end_date_edit.setDate(QDate())
            self.actual_end_date_edit.setSpecialValueText(" ")


    def update_filter_label(self, label_text):
        """フィルタラベルのテキストを更新する"""
        self.filter_label.setText(label_text)