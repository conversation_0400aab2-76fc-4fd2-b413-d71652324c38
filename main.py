import sys
from PyQt5.QtWidgets import QApplication
from app.database.db import init_db
from app.controllers.main_controller import MainController
from app.views.main_window import MainWindow
from app.utils.theme_manager import apply_theme

def main():
    init_db()
    
    app = QApplication(sys.argv)
    apply_theme(app)
    
    controller = MainController(app) # Pass app to controller
    
    main_window = MainWindow(controller)
    controller.view = main_window # Set the view for the controller
    main_window.show()
    controller.load_initial_data() # Load data after view is shown
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()