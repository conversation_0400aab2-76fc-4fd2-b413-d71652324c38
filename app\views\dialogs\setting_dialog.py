import os
from PyQt5.QtWidgets import (QVBoxLayout, 
                            QHBoxLayout, QPushButton, QMessageBox, QDialog, QFormLayout, QLineEdit)

from services.backlog_service import BacklogService

# --- BacklogSettingsDialog ---
class BacklogSettingsDialog(QDialog):
    """Backlog設定を管理するダイアログ"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle('Backlog設定')
        self.setMinimumWidth(500)
        self.setup_ui()
        self.load_settings()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        form_layout = QFormLayout()
        
        # Backlog URL
        self.base_url_edit = QLineEdit()
        self.base_url_edit.setEnabled(False)  # URLは固定
        form_layout.addRow('Backlog URL:', self.base_url_edit)
        
        # API Key
        self.api_key_edit = QLineEdit()
        self.api_key_edit.setPlaceholderText('APIキーを入力')
        form_layout.addRow('APIキー:', self.api_key_edit)
        
        # プロジェクト名
        self.project_name_edit = QLineEdit()
        self.project_name_edit.setPlaceholderText('プロジェクト名')
        form_layout.addRow('プロジェクト名:', self.project_name_edit)
        
        # プロジェクトID
        self.project_id_edit = QLineEdit()
        self.project_id_edit.setPlaceholderText('プロジェクトID (例: PROJ)')
        form_layout.addRow('プロジェクトID:', self.project_id_edit)
        
        layout.addLayout(form_layout)
        
        # 接続テストボタン
        test_button = QPushButton('接続テスト')
        test_button.clicked.connect(self.test_connection)
        layout.addWidget(test_button)
        
        # ボタンレイアウト
        button_layout = QHBoxLayout()
        cancel_button = QPushButton('キャンセル')
        cancel_button.clicked.connect(self.reject)
        save_button = QPushButton('保存')
        save_button.clicked.connect(self.save_settings)
        button_layout.addWidget(cancel_button)
        button_layout.addWidget(save_button)
        layout.addLayout(button_layout)
        
    def load_settings(self):
        """設定ファイルから設定を読み込む"""
        try:
            # BacklogServiceから設定を取得
            active_projects = BacklogService.get_project_list()
            if active_projects:
                project = active_projects[0]
                self.base_url_edit.setText(project.get('base_url', ''))
                self.api_key_edit.setText(project.get('api_key', ''))
                self.project_name_edit.setText(project.get('name', ''))
                self.project_id_edit.setText(project.get('id', ''))
        except Exception as e:
            QMessageBox.warning(self, "設定読み込みエラー", f"設定の読み込みに失敗しました: {str(e)}")
            
    def save_settings(self):
        """設定を保存する"""
        base_url = self.base_url_edit.text().strip()
        api_key = self.api_key_edit.text().strip()
        project_name = self.project_name_edit.text().strip()
        project_id = self.project_id_edit.text().strip()
        
        if not base_url or not api_key:
            QMessageBox.warning(self, "入力エラー", "BacklogのURLとAPIキーは必須です。")
            return
            
        if not project_name:
            project_name = "Backlog"  # デフォルト名
            
        # 設定ファイルにデータを保存
        config_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '../../config')
        settings_path = os.path.join(config_dir, 'settings.py')
        
        # 既存の設定を読み込む
        settings_data = {}
        try:
            if os.path.exists(settings_path):
                with open(settings_path, 'r', encoding='utf-8') as f:
                    settings_content = f.read()
                    # 既存の変数を抽出（簡易的な方法）
                    if 'BACKLOG_CREDENTIALS' in settings_content:
                        # ここでは簡易的な処理のため、既存のファイルを読み込んで書き換える
                        # 実際には、より堅牢なパーシングが必要
                        pass
        except Exception as e:
            QMessageBox.warning(self, "設定読み込みエラー", f"既存の設定の読み込みに失敗しました: {str(e)}")
            
        # 新しい設定内容
        new_settings = f"""# Backlog設定ファイル
BACKLOG_CREDENTIALS = [
    {{
        'name': '{project_name}',
        'id': '{project_id}',
        'base_url': '{base_url}',
        'api_key': '{api_key}',
        'disabled': False
    }}
]
"""
        try:
            with open(settings_path, 'w', encoding='utf-8') as f:
                f.write(new_settings)
            QMessageBox.information(self, "設定保存", "Backlog設定を保存しました。")
            self.accept()
        except Exception as e:
            QMessageBox.critical(self, "設定保存エラー", f"設定の保存に失敗しました: {str(e)}")
            
    def test_connection(self):
        """Backlogへの接続をテストする"""
        base_url = self.base_url_edit.text().strip()
        api_key = self.api_key_edit.text().strip()
        
        if not base_url or not api_key:
            QMessageBox.warning(self, "入力エラー", "BacklogのURLとAPIキーを入力してください。")
            return
            
        try:
            # 一時的なBacklogServiceインスタンスを作成
            temp_project = {
                'name': 'テスト接続',
                'base_url': base_url,
                'api_key': api_key
            }
            
            # 新しいBacklogServiceインスタンスを作成して接続テスト
            service = BacklogService()
            service.project = temp_project
            service.base_url = base_url
            service.api_key = api_key
            
            # 接続テスト
            user_info = service.get_myself()
            
            if user_info and 'id' in user_info:
                QMessageBox.information(
                    self, 
                    "接続成功", 
                    f"Backlogに正常に接続できました。\nユーザー: {user_info.get('name', 'Unknown')}"
                )
            else:
                QMessageBox.warning(self, "接続警告", "接続はできましたが、ユーザー情報を取得できませんでした。")
        except Exception as e:
            QMessageBox.critical(self, "接続エラー", f"Backlogへの接続に失敗しました。\n\nエラー詳細: {str(e)}")