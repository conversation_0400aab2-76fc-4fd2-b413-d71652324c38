"""
日報テンプレート設定ファイル
このファイルでは、タスク報告の出力フォーマットを定義します。
使用可能な変数:
- {title} - タスクのタイトル
- {issue_key} - タスクのIssue Key
- {start_date} - 予定開始日
- {due_date} - 予定終了日
- {progress} - 進捗状況
- {actual_start_date} - 実績開始日
- {actual_end_date} - 実績終了日
- {actual_time} - 実績時間
- {estimated_time} - 予定時間
- {comment} - コメント
- {source} - タスクのソース (backlog または local)

以下のテンプレートは変更可能です。
フォーマットを変更する際は、スペースやインデントを正確に保持してください。
"""
import os
import json

# デフォルトテンプレート
DEFAULT_TEMPLATE = {
    # プロジェクトタイトルのフォーマット
    'project_title': '● {project_name}\n',
    
    # タスクタイトルのフォーマット
    'task_title': '    {index}. {issue_key} {title}\n',
    
    # タスク詳細のフォーマット
    'task_details': '　　　　進捗状況：{progress}\n'
                    '　　　　予定期間：{start_date} ～ {due_date}\n'
                    '　　　　実績期間：{actual_period}\n'
                    '　　　　実績時間：{actual_time:.1f} h\n'
                    '　　　　遅れ要因：特になし\n'
                    '　　　　コメント：{comment}\n',
    
    # コメント履歴のヘッダーフォーマット
    'comment_history_header': '　　　　進捗記録：\n',
    
    # 各コメントのフォーマット
    'comment_entry': '　　　　　　　{timestamp} {text}\n',
    
    # タスク間の区切り
    'task_separator': '\n',
    
    # プロジェクト間の区切り
    'project_separator': '\n'
}

# カスタムテンプレート例
CUSTOM_TEMPLATE_1 = {
    'project_title': '■ {project_name}\n',
    
    'task_title': '  【{index}】{issue_key} {title}\n',
    
    'task_details': '    ・進捗: {progress}\n'
                    '    ・予定: {start_date} ～ {due_date}\n'
                    '    ・実績: {actual_period}\n'
                    '    ・時間: {actual_time:.1f}h\n'
                    '    ・備考: {comment}\n',
    
    'comment_history_header': '    ・履歴:\n',
    
    'comment_entry': '      [{timestamp}] {text}\n',
    
    'task_separator': '\n',
    
    'project_separator': '\n'
}

# 現在アクティブなテンプレート（設定で変更可能）
ACTIVE_TEMPLATE = DEFAULT_TEMPLATE

# 基本テンプレート
AVAILABLE_TEMPLATES = {
    'デフォルト': DEFAULT_TEMPLATE,
    'カスタム1': CUSTOM_TEMPLATE_1,
}

# カスタムテンプレートの読み込み
def load_custom_templates():
    """カスタムテンプレートをJSONファイルから読み込む"""
    try:
        # 現在のファイルの場所からパスを取得
        current_dir = os.path.dirname(os.path.abspath(__file__))
        custom_templates_path = os.path.join(current_dir, "custom_templates.json")
        
        print(f"カスタムテンプレートのパス: {custom_templates_path}")
        
        if os.path.exists(custom_templates_path):
            with open(custom_templates_path, 'r', encoding='utf-8') as f:
                custom_templates = json.load(f)
                
                print(f"読み込んだテンプレート: {list(custom_templates.keys())}")
                
                # テンプレートの検証と修正
                required_sections = [
                    'project_title', 'task_title', 'task_details', 
                    'comment_history_header', 'comment_entry',
                    'task_separator', 'project_separator'
                ]
                
                # すべてのテンプレートを検証
                for template_name, template in list(custom_templates.items()):
                    # 欠けているセクションがあれば、デフォルト値で補完
                    for section in required_sections:
                        if section not in template or template[section] is None:
                            print(f"警告: テンプレート '{template_name}' に '{section}' が不足しています。デフォルト値を使用します。")
                            template[section] = DEFAULT_TEMPLATE[section]
                    
                    # project_title で不適切な変数が使用されていないか確認
                    project_title = template.get('project_title', '')
                    if '{title}' in project_title:
                        print(f"警告: テンプレート '{template_name}' の project_title に 'title' が使用されています。")
                        template['project_title'] = project_title.replace('{title}', '{project_name}')
                
                # 読み込んだテンプレートを利用可能なテンプレートに追加
                AVAILABLE_TEMPLATES.update(custom_templates)
                
                print(f"{len(custom_templates)}個のカスタムテンプレートを読み込みました")
                print(f"利用可能なテンプレート: {list(AVAILABLE_TEMPLATES.keys())}")
        else:
            print(f"カスタムテンプレートファイルが存在しません: {custom_templates_path}")
    except Exception as e:
        print(f"カスタムテンプレートの読み込みエラー: {e}")
        import traceback
        traceback.print_exc()

# 起動時にカスタムテンプレートを読み込む
load_custom_templates()
