from PyQt5.QtWidgets import (
    Q<PERSON>ialog, QVBoxLayout, QHBoxLayout, QTextEdit, QPushButton, QComboBox, QLabel, QMessageBox
)
from PyQt5.QtCore import QTimer

class ReportDialog(QDialog):
    """A dialog for displaying and interacting with the generated report."""
    def __init__(self, report_text, parent=None):
        super().__init__(parent)
        self.setWindowTitle("日報生成")
        self.setGeometry(150, 150, 700, 550)

        self.report_text_content = report_text
        self.controller = None # Controller will be set after initialization

        self.setup_ui()

    def setup_ui(self):
        main_layout = QVBoxLayout(self)

        # Template selection
        template_layout = QHBoxLayout()
        template_layout.addWidget(QLabel("テンプレート:"))
        self.template_combo = QComboBox()
        template_layout.addWidget(self.template_combo)

        self.edit_template_btn = QPushButton("テンプレート編集")
        template_layout.addWidget(self.edit_template_btn)
        template_layout.addStretch()
        main_layout.addLayout(template_layout)

        # Preview
        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        self.preview_text.setText(self.report_text_content)
        main_layout.addWidget(self.preview_text)

        # Buttons
        button_layout = QHBoxLayout()
        self.copy_button = QPushButton("クリップボードにコピー")
        button_layout.addWidget(self.copy_button)
        main_layout.addLayout(button_layout)

    def set_controller(self, controller):
        """Set the controller and connect signals."""
        self.controller = controller
        self.copy_button.clicked.connect(self.controller.copy_to_clipboard)
        self.template_combo.currentTextChanged.connect(self.controller.change_template)
        self.edit_template_btn.clicked.connect(self.controller.edit_templates)

    def set_template_list(self, templates):
        self.template_combo.blockSignals(True)
        self.template_combo.clear()
        self.template_combo.addItems(templates)
        self.template_combo.blockSignals(False)

    def set_selected_template(self, template_name):
        self.template_combo.blockSignals(True)
        self.template_combo.setCurrentText(template_name)
        self.template_combo.blockSignals(False)

    def update_preview(self, report_text):
        self.preview_text.setText(report_text)

    def show_message(self, title, message):
        QMessageBox.information(self, title, message)
