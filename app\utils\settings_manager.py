import json
import os

SETTINGS_FILE = 'config/settings.json'

def _load_settings():
    if not os.path.exists(SETTINGS_FILE):
        return {}
    try:
        with open(SETTINGS_FILE, 'r') as f:
            return json.load(f)
    except (<PERSON><PERSON><PERSON><PERSON>, json.JSONDecodeError):
        return {}

def _save_settings(settings):
    os.makedirs(os.path.dirname(SETTINGS_FILE), exist_ok=True)
    with open(SETTINGS_FILE, 'w') as f:
        json.dump(settings, f, indent=4)

def get_setting(key, default=None):
    settings = _load_settings()
    return settings.get(key, default)

def set_setting(key, value):
    settings = _load_settings()
    settings[key] = value
    _save_settings(settings)
