from sqlalchemy import Column, Integer, String, DateTime, Boolean, Float, func
from sqlalchemy.orm import relationship
from .base import Base
from .custom_types import CustomDateTime

class Task(Base):
    __tablename__ = 'tasks'
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False)
    description = Column(String)
    due_date = Column(CustomDateTime, nullable=True)
    status = Column(String(50), default='未着手') 
    project = Column(String(100), nullable=True)
    completed = Column(Boolean, nullable=False, default=False)
    repeat = Column(String, nullable=True) 
    start_date = Column(CustomDateTime, nullable=True)
    estimated_time = Column(Float, nullable=True) 
    actual_time = Column(Float, nullable=True, default=0.0) 
    actual_start_date = Column(CustomDateTime, nullable=True)
    actual_end_date = Column(CustomDateTime, nullable=True)
    
    source = Column(String(50), nullable=True) 
    issue_key = Column(String(50), nullable=True, index=True)
    backlog_project_id = Column(String(50), nullable=True) # Backlog project ID
    source_url = Column(String(512), nullable=True) # URL to the task in the source system
    reminder_enabled = Column(Boolean, nullable=False, default=False)
    reminder_days = Column(Integer, nullable=True) #
    
    # 
    comments = relationship("Comment", back_populates="task", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Task(id={self.id}, title='{self.title}', status='{self.status}')>"    
    
    @property
    def completed_status(self):
        """Property that returns True if status is '完了' for backward compatibility"""
        return self.status == '完了'
    
    @completed_status.setter
    def completed_status(self, value):
        """Setter that updates both completed field and status for consistency"""
        self.completed = value
        if value:
            self.status = '完了'
        elif self.status == '完了':
            self.status = '未着手'
    
    def repeat_display_text(self):
        if not self.repeat:
            return "なし" # None or "なし"
        return self.repeat

    def get_next_repeat_date(self):
        # Placeholder -
        # This logic needs to be carefully implemented based on original task_manager.py
        # and how repeat are stored and interpreted.
        from datetime import datetime, timedelta
        if not self.repeat or not self.due_date:
            return None

        if self.repeat == "daily":
            return self.due_date + timedelta(days=1)
        elif self.repeat == "weekly":
            return self.due_date + timedelta(weeks=1)
        # ... more complex repeat logic
        return None