from PyQt5.QtCore import QObject
from PyQt5.QtWidgets import QApplication
from app.services.report_service import ReportService
from app.views.dialogs.report_dialog import ReportDialog
from app.views.dialogs.template_editor_dialog import TemplateEditorDialog
from config.report_templates import AVAILABLE_TEMPLATES, load_custom_templates, DEFAULT_TEMPLATE

class ReportController(QObject):
    def __init__(self, selected_tasks, parent_controller=None):
        super().__init__()
        self.parent_controller = parent_controller
        self.selected_tasks = selected_tasks
        self.report_service = ReportService()
        self.current_template_name = 'デフォルト'
        self.current_template = DEFAULT_TEMPLATE

        # Initial report generation
        report_text = self.report_service.generate_report(self.selected_tasks, self.current_template)

        # Setup UI
        self.view = ReportDialog(report_text)
        self.view.set_controller(self)

        self.load_templates()

    def show_dialog(self):
        self.view.exec_()

    def load_templates(self):
        load_custom_templates() # Reload from file
        self.view.set_template_list(list(AVAILABLE_TEMPLATES.keys()))
        self.view.set_selected_template(self.current_template_name)

    def change_template(self, template_name):
        if template_name in AVAILABLE_TEMPLATES:
            self.current_template_name = template_name
            self.current_template = AVAILABLE_TEMPLATES[template_name]
            self.update_report_preview()
        else:
            print(f"Warning: Template '{template_name}' not found.")

    def update_report_preview(self):
        report_text = self.report_service.generate_report(self.selected_tasks, self.current_template)
        self.view.update_preview(report_text)

    def copy_to_clipboard(self):
        clipboard = QApplication.clipboard()
        clipboard.setText(self.view.preview_text.toPlainText())
        self.view.show_message("コピー完了", "日報テキストをクリップボードにコピーしました。")

    def edit_templates(self):
        """テンプレート編集ダイアログを開く"""
        template_editor = TemplateEditorDialog(self.view)
        result = template_editor.exec_()

        if result == template_editor.Accepted:
            # テンプレートが変更された可能性があるので、テンプレートリストを再読み込み
            self.load_templates()
            # 現在のテンプレートで報告を再生成
            self.update_report_preview()
