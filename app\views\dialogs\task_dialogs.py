from PyQt5.QtWidgets import (
    QDialog, QLineEdit, QFormLayout, QDateEdit, QComboBox, QTextEdit,
    QPushButton, QHBoxLayout, QLabel, QDoubleSpinBox, QCheckBox, QSpinBox,
    QGroupBox, QVBoxLayout
)
from PyQt5.QtCore import QDate, Qt

# --- AddTaskDialog ---
class AddTaskDialog(QDialog):
    def __init__(self, projects=None, reminder_settings=None, parent=None):
        super().__init__(parent)
        self.setWindowTitle('新しいタスクを追加')
        self.setMinimumWidth(400)
        self.projects = projects or []
        self.reminder_settings = reminder_settings or {'enabled': True, 'default_timing': 1}
        self.setup_ui()

    def keyPressEvent(self, event):
        # Enter キーが押された場合、通常のキーイベント処理を行わず、accept() を呼び出す
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            # Shift キーが同時に押されている場合は通常の改行動作を許可
            if event.modifiers() & Qt.ShiftModifier and isinstance(self.focusWidget(), QTextEdit):
                super().keyPressEvent(event)
            else:
                # タイトルが入力されているか確認
                if not self.title_edit.text().strip():
                    # タイトルが空の場合は、タイトル入力欄にフォーカスを移動
                    self.title_edit.setFocus()
                    # タイトル欄を強調表示
                    self.title_edit.setStyleSheet("background-color: #ffcccc;")
                    return
                # フォームを確定
                self.accept()
        else:
            # その他のキーイベントは通常処理
            super().keyPressEvent(event)

    def setup_ui(self):
        layout = QFormLayout(self)

        self.title_edit = QLineEdit()
        layout.addRow('タイトル:', self.title_edit)

        # Add the date inputs
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setDate(QDate.currentDate())
        layout.addRow('開始日:', self.start_date_edit)

        self.due_date_edit = QDateEdit()
        self.due_date_edit.setCalendarPopup(True)
        self.due_date_edit.setDate(QDate.currentDate())
        layout.addRow('期限日:', self.due_date_edit)

        # Add status dropdown
        self.status_combo = QComboBox()
        self.status_combo.addItems(['未着手', '進行中', '完了', '遅延'])
        layout.addRow('状態:', self.status_combo)

        # Add repeat dropdown
        self.repeat_combo = QComboBox()
        self.repeat_combo.addItems(['なし', '毎日', '毎週', '毎月'])
        layout.addRow('繰り返し:', self.repeat_combo)

        # Add project dropdown
        self.project_combo = QComboBox()
        self.project_combo.addItem('なし')  # No project
        if self.projects:
            for project in self.projects:
                if project and project != 'なし':  # Skip null or empty project names
                    self.project_combo.addItem(project)
        layout.addRow('プロジェクト:', self.project_combo)

        # Add task description field
        self.description_edit = QTextEdit()
        layout.addRow('説明:', self.description_edit)

        # Time estimation controls
        self.estimated_time_spin = QDoubleSpinBox()
        self.estimated_time_spin.setRange(0, 999.9)
        self.estimated_time_spin.setDecimals(1)
        self.estimated_time_spin.setSuffix(" h")
        layout.addRow('予定時間:', self.estimated_time_spin)

        # Actual time controls
        self.actual_time_spin = QDoubleSpinBox()
        self.actual_time_spin.setRange(0, 999.9)
        self.actual_time_spin.setDecimals(1)
        self.actual_time_spin.setSuffix(" h")
        layout.addRow('実績時間:', self.actual_time_spin)

        # リマインダー設定（グループボックス内）
        reminder_group = QGroupBox("リマインダー設定")
        reminder_layout = QVBoxLayout()
        
        # グローバル設定の取得
        global_enabled = self.reminder_settings.get('enabled', True)
        global_timing = self.reminder_settings.get('default_timing', 0)
        
        # タスク固有の設定（編集時のみ）
        self.task_reminder_enabled = global_enabled
        self.task_reminder_days = None  # None means use global setting
        
        # リマインダー有効化チェックボックス
        self.reminder_enabled_checkbox = QCheckBox("リマインダーを有効にする")
        self.reminder_enabled_checkbox.setChecked(self.task_reminder_enabled)
        reminder_layout.addWidget(self.reminder_enabled_checkbox)
        
        # タイミング設定用のHBoxLayout
        timing_layout = QHBoxLayout()
        
        # カスタムタイミングのチェックボックス
        self.custom_timing_checkbox = QCheckBox("カスタム通知タイミング：")
        self.custom_timing_checkbox.stateChanged.connect(self.toggle_custom_timing)
        timing_layout.addWidget(self.custom_timing_checkbox)
        
        # カスタムタイミングのスピンボックス
        self.custom_timing_spinbox = QSpinBox()
        self.custom_timing_spinbox.setRange(0, 30)  # 0-30日前
        self.custom_timing_spinbox.setSuffix("日前")
        self.custom_timing_spinbox.setValue(global_timing)  # グローバル設定をデフォルト値として使用
        self.custom_timing_spinbox.setEnabled(False)  # 初期状態では無効
        timing_layout.addWidget(self.custom_timing_spinbox)
        
        reminder_layout.addLayout(timing_layout)
        reminder_group.setLayout(reminder_layout)
        layout.addRow(reminder_group)

        # Add buttons
        button_layout = QHBoxLayout()
        self.add_button = QPushButton('追加')
        self.add_button.clicked.connect(self.accept)
        button_layout.addWidget(self.add_button)
        
        self.cancel_button = QPushButton('キャンセル')
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        layout.addRow('', button_layout)

    def toggle_custom_timing(self, state):
        """カスタム時間設定の有効/無効を切り替える"""
        self.custom_timing_spinbox.setEnabled(state == Qt.Checked)

    def accept(self):
        """OKボタンが押されたとき、追加前にタイトルのバリデーションをチェック"""
        if not self.title_edit.text().strip():
            # タイトルが空の場合は、タイトル入力欄にフォーカスを移動して処理を中止
            self.title_edit.setFocus()
            self.title_edit.setStyleSheet("background-color: #ffcccc;")
            return
        
        # 親クラスの accept メソッドを呼び出して、ダイアログを閉じる
        super().accept()
        
    def get_task_data(self):
        repeat_map = {
            'なし': None,
            '毎日': 'daily',
            '毎週': 'weekly',
            '毎月': 'monthly'
        }

        project = self.project_combo.currentText()
        if project == 'なし':
            project = ''
        
        # リマインダー設定取得
        reminder_enabled = self.reminder_enabled_checkbox.isChecked()
        reminder_days = self.custom_timing_spinbox.value() if self.custom_timing_checkbox.isChecked() else None
        
        return {
            'title': self.title_edit.text(),
            'start_date': self.start_date_edit.date().toString('yyyy/MM/dd'), # 開始日を取得
            'due_date': self.due_date_edit.date().toString('yyyy/MM/dd'), # 終了日(期限日)を取得
            'status': self.status_combo.currentText(), # 状態を取得
            'repeat': repeat_map[self.repeat_combo.currentText()],
            'description': self.description_edit.toPlainText(),
            'project': project,
            'estimated_time': self.estimated_time_spin.value(), # 予定時間を取得
            'reminder_enabled': reminder_enabled,
            'reminder_days': reminder_days
        }

# --- EditTaskDialog ---
class EditTaskDialog(AddTaskDialog):
    def __init__(self, task=None, projects=None, reminder_settings=None, task_reminder_settings=None, parent=None):
        super().__init__(projects=projects, reminder_settings=reminder_settings, parent=parent)
        self.setWindowTitle('タスクを編集')
        self.task = task
        self.task_reminder_settings = task_reminder_settings or {'enabled': True, 'days': None}
        self.fill_task_data()
        self.add_button.setText('更新') # ボタンテキスト変更

    def fill_task_data(self):
        if not self.task:
            return

        self.title_edit.setText(self.task.title or "") # Handle None title

        # Helper to set QDateEdit from datetime or date object
        def _set_qdate_edit(q_date_edit_widget, dt_obj):
            if dt_obj and hasattr(dt_obj, 'year') and hasattr(dt_obj, 'month') and hasattr(dt_obj, 'day'):
                q_date_edit_widget.setDate(QDate(dt_obj.year, dt_obj.month, dt_obj.day))
                q_date_edit_widget.setSpecialValueText("") # Clear special text if date is valid
            else:
                q_date_edit_widget.setDate(QDate()) # Invalid or None date
                q_date_edit_widget.setSpecialValueText(" ") # Show as blank

        # --- 予定期間を設定 ---
        _set_qdate_edit(self.start_date_edit, getattr(self.task, 'start_date', None))
        _set_qdate_edit(self.due_date_edit, getattr(self.task, 'due_date', None))
        
        # --- 状態と完了 ---
        status = getattr(self.task, 'status', '未着手')
        index = self.status_combo.findText(status)
        if index >= 0:
            self.status_combo.setCurrentIndex(index)

        # --- 繰り返し ---
        repeat_value_to_text = {
            None: 'なし',
            'daily': '毎日',
            'weekly': '毎週',
            'monthly': '毎月'
        }
        repeat_setting = getattr(self.task, 'repeat', None)
        repeat_text = repeat_value_to_text.get(repeat_setting, 'なし')
        index = self.repeat_combo.findText(repeat_text)
        if index >= 0:
            self.repeat_combo.setCurrentIndex(index)

        # --- プロジェクト ---
        project = getattr(self.task, 'project', '') or ''
        if not project:
            project = 'なし'
        index = self.project_combo.findText(project)
        if index >= 0:
            self.project_combo.setCurrentIndex(index)

        # --- 説明 ---
        self.description_edit.setPlainText(getattr(self.task, 'description', ""))

        # --- 時間関連 ---
        self.estimated_time_spin.setValue(getattr(self.task, 'estimated_time', 0.0) or 0.0)
        self.actual_time_spin.setValue(getattr(self.task, 'actual_time', 0.0) or 0.0)
        
        # --- リマインダー設定 ---
        # タスク固有の設定を取得
        task_reminder = self.task_reminder_settings
        self.reminder_enabled_checkbox.setChecked(task_reminder['enabled'])
        
        # カスタムタイミングが設定されている場合
        if task_reminder['days'] is not None:  # None でない場合はカスタム設定
            self.custom_timing_checkbox.setChecked(True)
            self.custom_timing_spinbox.setValue(task_reminder['days'])
            self.custom_timing_spinbox.setEnabled(True)
        else:
            self.custom_timing_checkbox.setChecked(False)
            self.custom_timing_spinbox.setEnabled(False)

    def get_task_data(self):
        # 親クラスの get_task_data メソッドを呼び出して基本データを取得
        task_data = super().get_task_data()
        
        # 実績時間など、編集ダイアログでのみ設定できる項目を追加
        task_data['actual_time'] = self.actual_time_spin.value()
        
        return task_data
