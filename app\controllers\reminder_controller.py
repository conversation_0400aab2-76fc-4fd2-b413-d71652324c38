from PyQt5.QtWidgets import QDialog, QMessageBox
from app.views.dialogs.reminder_dialog import ReminderSettingsDialog
from app.database.reminder_manager import get_reminder_settings, update_reminder_settings
from app.services.reminder_service import ReminderSettings, load_custom_reminder_settings

class ReminderController:
    def __init__(self, parent=None):
        self.parent = parent
        self.view = parent.view if hasattr(parent, 'view') else parent

    def show_settings_dialog(self):
        """
        Shows the reminder settings dialog, loads the current settings,
        and saves them if the user confirms.
        """
        try:
            # Get current settings from the database
            current_settings = get_reminder_settings()

            # Create and show the dialog, passing the current settings
            dialog = ReminderSettingsDialog(parent=self.view, initial_settings=current_settings)

            # If the user clicks "Save" (dialog is accepted)
            if dialog.exec_() == QDialog.Accepted:
                # Get the new settings from the dialog
                new_settings = dialog.get_settings()

                # Update the settings in the database
                success = update_reminder_settings(
                    enabled=new_settings['enabled'],
                    default_timing=new_settings['default_timing']
                )

                if success:
                    # Update the reminder service if it exists
                    if hasattr(self.parent, 'reminder_service') and self.parent.reminder_service:
                        custom_settings = load_custom_reminder_settings()
                        reminder_settings = ReminderSettings(
                            enabled=new_settings['enabled'],
                            default_timing=new_settings['default_timing'],
                            custom_timing=custom_settings
                        )
                        self.parent.reminder_service.update_settings(reminder_settings)

                        # Restart service if needed
                        if new_settings['enabled']:
                            if not self.parent.reminder_service.thread or not self.parent.reminder_service.thread.isRunning():
                                self.parent.reminder_service.start()
                        else:
                            self.parent.reminder_service.stop()

                    QMessageBox.information(self.view, "設定保存", "リマインダー設定が保存されました。")
                else:
                    QMessageBox.warning(self.view, "エラー", "設定の保存に失敗しました。")

        except Exception as e:
            QMessageBox.critical(self.view, "エラー", f"リマインダー設定の表示に失敗しました: {e}")

    def show_reminder_status(self):
        """Show reminder status dialog"""
        try:
            # Get current settings
            settings = get_reminder_settings()
            custom_settings = load_custom_reminder_settings()

            # Check if reminder service is running
            service_status = "停止中"
            if hasattr(self.parent, 'reminder_service') and self.parent.reminder_service:
                if self.parent.reminder_service.thread and self.parent.reminder_service.thread.isRunning():
                    service_status = "実行中"

            # Create status message
            status_msg = f"""リマインダーサービス状況:

サービス状態: {service_status}
グローバル設定: {'有効' if settings.get('enabled', True) else '無効'}
デフォルト通知時間: {settings.get('default_timing', 1)}日前
カスタム設定数: {len(custom_settings)}個のタスク

カスタム設定詳細:"""

            if custom_settings:
                for task_id, days in custom_settings.items():
                    status_msg += f"\n  タスクID {task_id}: {days}日前"
            else:
                status_msg += "\n  なし"

            QMessageBox.information(self.view, "リマインダー状況", status_msg)

        except Exception as e:
            QMessageBox.critical(self.view, "エラー", f"リマインダー状況の取得に失敗しました: {e}")

    def debug_check_reminders(self):
        """Debug function to manually trigger reminder check"""
        try:
            if hasattr(self.parent, 'reminder_service') and self.parent.reminder_service:
                if self.parent.reminder_service.thread:
                    self.parent.reminder_service.thread.check_reminders()
                    QMessageBox.information(self.view, "デバッグ", "リマインダーチェックを手動実行しました。")
                else:
                    QMessageBox.warning(self.view, "警告", "リマインダーサービスが開始されていません。")
            else:
                QMessageBox.warning(self.view, "警告", "リマインダーサービスが初期化されていません。")
        except Exception as e:
            QMessageBox.critical(self.view, "エラー", f"リマインダーチェックに失敗しました: {e}")
