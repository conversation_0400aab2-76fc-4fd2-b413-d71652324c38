from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QListWidget, QListWidgetItem, QPushButton,
    QHBoxLayout, QLineEdit, QLabel, QMessageBox, QDialogButtonBox,
    QGroupBox, QFormLayout
)
from PyQt5.QtCore import Qt

class BacklogSettingsDialog(QDialog):
    def __init__(self, settings, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Backlog 設定")
        self.setMinimumWidth(500)
        self.settings = settings  # List of setting dicts

        self.main_layout = QVBoxLayout(self)

        # List of accounts
        self.account_list_widget = QListWidget()
        self.account_list_widget.itemDoubleClicked.connect(self.edit_account)
        self.main_layout.addWidget(self.account_list_widget)

        # Buttons for list
        button_layout = QHBoxLayout()
        self.add_btn = QPushButton("追加")
        self.edit_btn = QPushButton("編集")
        self.remove_btn = QPushButton("削除")
        button_layout.addWidget(self.add_btn)
        button_layout.addWidget(self.edit_btn)
        button_layout.addWidget(self.remove_btn)
        self.main_layout.addLayout(button_layout)

        # Dialog buttons
        self.button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        self.main_layout.addWidget(self.button_box)

        # Connect signals
        self.add_btn.clicked.connect(self.add_account)
        self.edit_btn.clicked.connect(self.edit_account)
        self.remove_btn.clicked.connect(self.remove_account)
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)

        self.populate_list()

    def populate_list(self):
        self.account_list_widget.clear()
        for setting in self.settings:
            name = setting.get('name', 'N/A')
            base_url = setting.get('base_url', 'N/A')
            item = QListWidgetItem(f"{name} ({base_url})")
            item.setData(Qt.UserRole, setting)
            self.account_list_widget.addItem(item)

    def add_account(self):
        dialog = AccountDetailDialog(parent=self)
        if dialog.exec_():
            new_setting = dialog.get_settings()
            self.settings.append(new_setting)
            self.populate_list()

    def edit_account(self):
        selected_item = self.account_list_widget.currentItem()
        if not selected_item:
            QMessageBox.warning(self, "警告", "編集するアカウントを選択してください。")
            return

        current_setting = selected_item.data(Qt.UserRole)
        dialog = AccountDetailDialog(settings=current_setting, parent=self)

        if dialog.exec_():
            updated_setting = dialog.get_settings()
            # Replace the old setting with the updated one
            index = self.account_list_widget.row(selected_item)
            self.settings[index] = updated_setting
            self.populate_list()
            # Restore selection
            self.account_list_widget.setCurrentRow(index)

    def remove_account(self):
        selected_item = self.account_list_widget.currentItem()
        if not selected_item:
            QMessageBox.warning(self, "警告", "削除するアカウントを選択してください。")
            return

        reply = QMessageBox.question(self, "確認", "このアカウント設定を削除しますか？",
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

        if reply == QMessageBox.Yes:
            index = self.account_list_widget.row(selected_item)
            del self.settings[index]
            self.populate_list()

    def get_settings(self):
        return self.settings

class AccountDetailDialog(QDialog):
    def __init__(self, settings=None, parent=None):
        super().__init__(parent)
        self.setWindowTitle("アカウント詳細")

        self.layout = QVBoxLayout(self)
        form_group = QGroupBox("Backlog 接続情報")
        form_layout = QFormLayout()

        self.name_input = QLineEdit()
        self.base_url_input = QLineEdit()
        self.api_key_input = QLineEdit()
        self.api_key_input.setEchoMode(QLineEdit.Password)

        form_layout.addRow(QLabel("アカウント名:"), self.name_input)
        form_layout.addRow(QLabel("スペースURL:"), self.base_url_input)
        form_layout.addRow(QLabel("APIキー:"), self.api_key_input)
        form_group.setLayout(form_layout)
        self.layout.addWidget(form_group)

        self.button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        self.layout.addWidget(self.button_box)

        self.button_box.accepted.connect(self.validate_and_accept)
        self.button_box.rejected.connect(self.reject)

        if settings:
            self.name_input.setText(settings.get('name', ''))
            self.base_url_input.setText(settings.get('base_url', ''))
            self.api_key_input.setText(settings.get('api_key', ''))

    def validate_and_accept(self):
        if not self.name_input.text().strip():
            QMessageBox.warning(self, "入力エラー", "アカウント名は必須です。")
            return
        if not self.base_url_input.text().strip() or not self.base_url_input.text().startswith('https'):
            QMessageBox.warning(self, "入力エラー", "有効なスペースURLを 'https://...' の形式で入力してください。")
            return
        if not self.api_key_input.text().strip():
            QMessageBox.warning(self, "入力エラー", "APIキーは必須です。")
            return
        self.accept()

    def get_settings(self):
        return {
            'name': self.name_input.text().strip(),
            'base_url': self.base_url_input.text().strip(),
            'api_key': self.api_key_input.text().strip(),
            'disabled': False # Default to enabled
        }
