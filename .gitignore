# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
dist/
build/
*.egg-info/

# Virtual environments
venv/
env/
.env/
.venv/
ENV/

# PyCharm
.idea/

# VS Code
.vscode/

# Jupyter Notebook
.ipynb_checkpoints

# Local configuration
config.ini
*.cfg
credentials.json

# Logs
*.log
logs/

# Database files
*.db
*.sqlite3

# OS specific files
.DS_Store
Thumbs.db
desktop.ini

# Backlog specific (if needed)
# Add any project-specific files/directories here
__pycache__/backlogServer.cpython-313.pyc
__pycache__/settings.cpython-313.pyc
